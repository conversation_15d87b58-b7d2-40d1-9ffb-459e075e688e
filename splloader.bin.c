/* This file was generated by the Hex-Rays decompiler version 9.0.0.241217.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: GNU C++
*/

#include <defs.h>


//-------------------------------------------------------------------------
// Function declarations

__int64 sub_65000AD0();
__int64 __fastcall sub_65000AE4(__int64 n258);
__int64 sub_65000B2C();
unsigned __int64 sub_65001BDC();
__int64 __fastcall sub_65001BE0(__int64 a1, __int64 a2, _QWORD *a3);
__int64 __fastcall sub_65001C10(__int64 a1, __int64 a2, int a3, __int64 a4);
bool __fastcall sub_65001C60(__int64 a1, __int64 a2, _QWORD *a3);
__int64 __fastcall sub_65001CE8(__int64 a1, __int64 a2, _QWORD *a3);
__int64 __fastcall sub_65001E28(_BYTE *a1);
__int64 sub_65001FE8();
__int64 __fastcall nullsub_1(_QWORD); // weak
void __fastcall __noreturn sub_65001FF8(_QWORD); // weak
__int64 __fastcall nullsub_2(_QWORD); // weak
__int64 __fastcall sub_65002000(__int64 result, char a2, __int64 i);
__int64 __fastcall sub_65002078(__int64 a1, __int64 a2);
__int64 __fastcall sub_650020A0(__int64 result, char a2, char a3, int a4);
__int64 __fastcall sub_650020D0(unsigned int a1, char a2, char a3);
__int64 __fastcall sub_650020F4(__int64 i);
__int64 __fastcall sub_6500215C(__int64 i);
_BYTE *__fastcall sub_650021A0(_BYTE *result);
_BYTE *__fastcall sub_650021D0(unsigned int n5);
_BYTE *__fastcall sub_65002260(_BYTE *result);
__int64 sub_65002264();
__int64 sub_65002328();
__int64 sub_6500243C();
__int64 sub_650025A4();
__int64 sub_650025FC();
__int64 sub_65002618();
__int64 sub_650026DC();
__int64 __fastcall sub_650027A8(int a1);
__int64 sub_65002868();
__int64 __fastcall sub_65002904(__int64 n3, _DWORD *n0x65010E68);
__int64 sub_6500299C();
__int64 sub_650029B4();
__int64 sub_650029CC();
__int64 sub_650029E4();
__int64 sub_650029FC();
__int64 __fastcall sub_65002A68(unsigned int a1);
__int64 sub_65002BE8();
__int64 sub_65002C20();
__int64 sub_65002D98();
__int64 sub_65002E10();
__int64 sub_65002ED0();
__int64 sub_65002F0C();
__int64 __fastcall sub_65002F14(unsigned int *a1);
__int64 sub_65002F4C();
__int64 sub_65002FA4();
__int64 sub_65002FE0();
unsigned __int64 __fastcall sub_650030B0(int a1, int n2, int a3);
__int64 sub_650031BC();
__int64 __fastcall sub_65003340(int a1, int a2, _DWORD *a3);
unsigned __int64 __fastcall sub_650035A0(int n2);
unsigned __int64 __fastcall sub_6500363C(unsigned int a1);
__int64 sub_650037F0();
__int64 sub_650039A0();
__int64 sub_65003A24();
__int64 sub_65003CDC();
__int64 sub_65003CFC();
unsigned __int64 __fastcall sub_65003D1C(__int64 n3);
__int64 sub_65003D6C();
__int64 sub_65003D88();
__int64 sub_650041B4();
__int64 sub_6500436C();
_BYTE *__fastcall sub_650043E8(__int64 n3, unsigned int n5);
_BYTE *sub_65004444();
__int64 sub_65004450();
__int64 __fastcall sub_6500454C(int a1, unsigned __int8 a2);
__int64 sub_6500466C();
__int64 __fastcall sub_650046B8(unsigned int n5);
__int64 sub_65004754();
__int64 __fastcall sub_650047AC(_QWORD); // weak
__int64 __fastcall sub_650047B4(_DWORD *a1);
__int64 sub_6500483C();
__int64 sub_65004888();
__int64 sub_650048D4();
__int64 __fastcall sub_65004A48(__int64 a1, int n2);
__int64 __fastcall sub_65004B70(char a1);
__int64 __fastcall sub_65004CFC(char a1);
__int64 sub_65004D2C();
int *sub_65004DA8();
__int64 sub_65004DFC();
__int64 sub_65004F90();
__int64 __fastcall sub_65005138(_QWORD); // weak
__int64 __fastcall sub_65005140(_DWORD *a1);
unsigned __int64 sub_650051C4();
__int64 __fastcall sub_65005258(int a1);
__int64 sub_650055E0();
__int64 sub_65005788();
__int64 __fastcall sub_650057F4(int n2);
__int64 sub_65005C68();
__int64 sub_6500611C();
__int64 sub_6500655C();
int *sub_65006890();
__int64 sub_65006AB8();
__int64 __fastcall sub_65006C14(unsigned int n4, int a2);
__int64 __fastcall sub_65006DB8(__int64 n2);
__int64 sub_65006DE8();
__int64 sub_65006EA0();
int *sub_65006F14();
int *sub_65006F98();
__int64 __fastcall sub_65006FB0(unsigned int n0x215, unsigned int a2);
bool __fastcall sub_650072A8(int a1);
__int64 sub_650073B0();
__int64 __fastcall sub_650074E8(unsigned int n255, int a2);
void *sub_65007678();
__int64 sub_65007768();
__int64 sub_65007C30();
__int64 sub_65007D30();
__int64 sub_65007DA8();
__int64 sub_650080F4();
__int64 sub_65008144();
unsigned __int64 __fastcall sub_650081E4(int a1);
__int64 __fastcall sub_65008288(int a1, int a2, int n2, int a4, int a5, __int64 a6);
__int64 __fastcall sub_6500854C(unsigned int *n0x65010EF8, __int64, __int64);
__int64 __fastcall sub_65008650(unsigned __int64 a1, unsigned __int64 a2);
__int64 __fastcall sub_6500880C(int a1, unsigned __int64 a2, unsigned __int64 a3);
__int64 __fastcall sub_65008854(int a1);
__int64 __fastcall sub_65008890(unsigned int a1, unsigned __int16 a2);
__int64 __fastcall sub_65008940(int a1);
__int64 __fastcall sub_6500895C(unsigned int a1, unsigned __int16 a2);
__int64 __fastcall sub_6500897C(unsigned int a1, __int16 a2, __int16 a3);
__int64 __fastcall sub_650089EC(_DWORD *a1);
__int64 __fastcall sub_65008A8C(_DWORD *a1, unsigned __int8 a2);
__int64 __fastcall sub_65008ABC(__int64 a1, unsigned int a2);
__int64 __fastcall sub_65008AFC(__int64 a1);
__int64 __fastcall sub_65008B84(__int64 a1, unsigned __int64 n0x18CBA80);
bool sub_650091D0();
unsigned __int64 __fastcall sub_6500940C(unsigned __int64 n0x65010F40, int a2);
__int64 __fastcall sub_650094B0(__int64 *n0x65010F40);
unsigned __int64 sub_650094C4();
__int64 __fastcall sub_650094D0(unsigned int a1);
__int64 __fastcall sub_65009508(__int64 *n0x65010F40, int);
__int64 __fastcall sub_65009574(__int64 n0x65010F40, unsigned int a2, int a3, __int64 a4, _BYTE *a5);
__int64 __fastcall sub_65009C10(__int64 n0x65010F40, _BYTE *a2);
__int64 __fastcall sub_65009C98(__int64 n0x65010F40);
__int64 __fastcall sub_65009CFC(__int64 n0x65010F40, unsigned __int8 n0xB3, unsigned __int8 n9);
bool __fastcall sub_65009D5C(int n7, int a2, int a3, __int64 a4);
__int64 sub_65009EF0();
__int64 sub_6500A1E8();
__int64 __fastcall sub_6500A1FC(_QWORD, _QWORD); // weak
char *__fastcall sub_6500A200(__int64 a1);
__int64 __fastcall sub_6500A288(__int64 a1, int a2);
__int64 sub_6500A364();
__int64 __fastcall sub_6500A410(__int64 a1);
__int64 __fastcall sub_6500A484(__int64 a1, int a2);
__int64 __fastcall sub_6500A53C(int a1);
__int64 sub_6500A5C8();
__int64 __fastcall sub_6500A6F0(_BYTE *i);
__int64 __fastcall sub_6500A70C(int n2);
__int64 __fastcall sub_6500A760(int a1);
__int64 sub_6500A7AC();
__int64 __fastcall sub_6500A840(int *a1);
__int64 __fastcall sub_6500AB54(__int64 a1, unsigned int n0x200, __int64 a3, __int64 a4);
__int64 __fastcall sub_6500AC14(__int64 a1, unsigned int n0x200, __int64 a3, __int64 a4);
bool sub_6500AC18();
__int64 __fastcall sub_6500AC74(int a1);
__int64 sub_6500AD18();
bool __fastcall sub_6500B030(__int64 a1, __int64 a2);
__int64 sub_6500B038(void); // weak
bool __fastcall sub_6500B03C(__int64 a1, unsigned int a2);
bool __fastcall sub_6500B05C(__int64 a1);
__int64 __fastcall sub_6500B0AC(unsigned int n0xFE, _DWORD *a2);
__int64 __fastcall sub_6500B274(unsigned int n3, int a2);
__int64 sub_6500B2F0();
__int64 sub_6500B358();
__int64 __fastcall sub_6500B394(unsigned int i_1, unsigned int i, __int64 a3, int a4);
bool __fastcall sub_6500B594(unsigned int i_1, unsigned int i, __int64 a3, int a4);
__int64 __fastcall sub_6500B840(unsigned int n0xFE, int a2);
__int64 __fastcall sub_6500B890(unsigned int n0xFE, int a2);
__int64 __fastcall sub_6500BAD8(int a1, __int64 a2);
__int64 __fastcall sub_6500BB3C(__int64 result, char a2, int i);
__int64 __fastcall sub_6500BB58(__int64 result, __int64 a2, int i);
__int64 __fastcall sub_6500BB78(__int64 a1, __int64 a2, int a3);
_BYTE *__fastcall sub_6500BBA8(_BYTE *a1, int n48, __int64 a3);
__int64 sub_6500BC00();
__int64 __fastcall sub_6500BCAC(int a1, unsigned __int64 n0x1234);
__int64 __fastcall sub_6500BD84(__int64 a1, __int64 a2, unsigned __int8 *a3);
__int64 __fastcall sub_6500C080(__int64 a1);
__int64 __fastcall sub_6500C094(__int64 a1);
__int64 __fastcall sub_6500C0A8(__int64 a1, __int64 a2);
__int64 __fastcall sub_6500C108(__int64 a1, __int64 a2);
bool __fastcall sub_6500C154(__int64 a1, __int64 a2);
_BYTE *__fastcall sub_6500C1B4(_BYTE *result);
__int64 __fastcall sub_6500C1E0(unsigned __int16 a1, unsigned int a2, __int64 a3);
__int64 __fastcall sub_6500C270(unsigned __int16 a1, unsigned int a2, __int64 a3);
__int64 __fastcall sub_6500C2B0(_DWORD *a1, int a2);
__int64 __fastcall sub_6500C2E0(int *a1);
__int64 __fastcall sub_6500C3D0(int *a1);
__int64 __fastcall sub_6500C744(int *a1);
__int64 sub_6500C89C();
__int64 sub_6500C8C0();
__int64 __fastcall sub_6500C8E4(int *a1);
int *__fastcall sub_6500CAB8(int *a1);
__int64 sub_6500CB00();
__int64 sub_6500CB18();
__int64 __fastcall sub_6500CB48(_QWORD); // weak
__int64 __fastcall sub_6500CB50(int *a1);
__int64 sub_6500CBB0();
unsigned __int64 sub_6500CBCC();
__int64 __fastcall nullsub_3(_QWORD); // weak
__int64 __fastcall sub_6500CEF4(__int64 a1, __int64 a2, unsigned int n512, _BYTE *n0x650110D8, int n48, int *a6, int *a7);
__int64 __fastcall sub_6500D0B4(_BYTE *a1, int n48, __int64 a3);
__int64 sub_6500D10C();
__int64 __fastcall sub_6500D18C(char a1, char a2);
__int64 nullsub_4(void); // weak
__int64 __fastcall nullsub_5(_QWORD); // weak
__int64 sub_6500D868();
__int64 __fastcall sub_6500DB1C(__int64 a1);
__int64 sub_6500DB40();

//-------------------------------------------------------------------------
// Data declarations

_UNKNOWN sub_6500201C;
_UNKNOWN sub_65002810;
_UNKNOWN sub_65002B8C;
_UNKNOWN sub_650034B0;
_UNKNOWN sub_6500376C;
_UNKNOWN sub_65003B48;
_UNKNOWN sub_65003BAC;
_UNKNOWN sub_650041E0;
_UNKNOWN loc_650043FC; // weak
_UNKNOWN sub_65004BA8;
_UNKNOWN sub_650052E8;
_UNKNOWN loc_650053FC; // weak
_UNKNOWN sub_6500600C;
_UNKNOWN sub_650065CC;
_UNKNOWN sub_65007D9C;
_UNKNOWN sub_65008028;
_UNKNOWN sub_6500818C;
_UNKNOWN sub_65008214;
_UNKNOWN sub_65008240;
_UNKNOWN sub_65008520;
_UNKNOWN sub_65009160;
_UNKNOWN sub_6500928C;
_UNKNOWN sub_6500936C;
_UNKNOWN sub_65009538;
_UNKNOWN sub_6500A664;
_UNKNOWN sub_6500AA0C;
_UNKNOWN sub_6500AAE4;
_UNKNOWN sub_6500B070;
_UNKNOWN sub_6500B6B4;
_UNKNOWN sub_6500B910;
_UNKNOWN sub_6500B970;
_UNKNOWN sub_6500BBBC;
_UNKNOWN sub_6500C220;
_UNKNOWN sub_6500CC04;
_UNKNOWN sub_6500D114;
_UNKNOWN sub_6500D13C;
_UNKNOWN sub_6500D164;
_UNKNOWN sub_6500D1A0;
_UNKNOWN sub_6500D334;
_UNKNOWN sub_6500D640;
_UNKNOWN sub_6500D77C;
_BYTE byte_6500DDB0[200] =
{
  1,
  1,
  2,
  2,
  3,
  3,
  4,
  4,
  4,
  0,
  0,
  0,
  5,
  0,
  0,
  0,
  6,
  0,
  0,
  0,
  7,
  0,
  0,
  0,
  8,
  0,
  0,
  0,
  16,
  0,
  0,
  0,
  15,
  0,
  0,
  0,
  14,
  0,
  0,
  0,
  13,
  0,
  0,
  0,
  12,
  0,
  0,
  0,
  11,
  0,
  0,
  0,
  10,
  0,
  0,
  0,
  9,
  0,
  0,
  0,
  17,
  0,
  0,
  0,
  18,
  0,
  0,
  0,
  19,
  0,
  0,
  0,
  20,
  0,
  0,
  0,
  21,
  0,
  0,
  0,
  22,
  0,
  0,
  0,
  23,
  0,
  0,
  0,
  24,
  0,
  0,
  0,
  32,
  0,
  0,
  0,
  31,
  0,
  0,
  0,
  30,
  0,
  0,
  0,
  29,
  0,
  0,
  0,
  28,
  0,
  0,
  0,
  27,
  0,
  0,
  0,
  26,
  0,
  0,
  0,
  25,
  0,
  0,
  0,
  36,
  0,
  0,
  0,
  35,
  0,
  0,
  0,
  34,
  0,
  0,
  0,
  33,
  0,
  0,
  0,
  40,
  0,
  0,
  0,
  39,
  0,
  0,
  0,
  38,
  0,
  0,
  0,
  37,
  0,
  0,
  0,
  41,
  0,
  0,
  0,
  42,
  0,
  0,
  0,
  43,
  0,
  0,
  0,
  44,
  0,
  0,
  0,
  45,
  0,
  0,
  0,
  46,
  0,
  0,
  0,
  47,
  0,
  0,
  0,
  48,
  0,
  0,
  0,
  49,
  0,
  0,
  0,
  50,
  0,
  0,
  0,
  0,
  0,
  0,
  0
}; // weak
int dword_6500E4F0 = 0; // weak
_UNKNOWN unk_6500E570; // weak
char aSmlA[6] = "sml_a"; // weak
char aTeecfgA[9] = "teecfg_a"; // weak
char aTrustosA[10] = "trustos_a"; // weak
char aUbootA[8] = "uboot_a"; // weak
_UNKNOWN unk_6500F190; // weak
_UNKNOWN unk_6500F1D0; // weak
_UNKNOWN unk_6500F210; // weak
_UNKNOWN unk_6500F250; // weak
int dword_6500F290 = -1; // weak
_UNKNOWN unk_6500F298; // weak
int dword_6500F2B8 = 1600; // weak
_UNKNOWN unk_6500F2C8; // weak
_DWORD dword_6500F2D0[4] = { 112, 132, 4208, 4228 }; // weak
_DWORD dword_6500F2E0[4] = { 108, 128, 4204, 4224 }; // weak
_UNKNOWN unk_6500F2F0; // weak
_UNKNOWN unk_6500F310; // weak
unsigned int dword_6500F328[4] = { 104u, 124u, 4200u, 4220u }; // weak
_DWORD dword_6500F338[4] = { 120, 140, 4216, 4236 }; // weak
_UNKNOWN unk_6500F348; // weak
_UNKNOWN unk_6500F648; // weak
_UNKNOWN unk_6500F7A8; // weak
_UNKNOWN unk_6500F848; // weak
_UNKNOWN unk_6500FE88; // weak
_DWORD dword_6500FED0[8] = { 335872320, 83968080, 178782228, -1476351360, 11173898, -1434449240, -12, -1 }; // weak
_DWORD dword_6500FEF0[4] = { 13959370, 14090443, 13238485, 15204564 }; // weak
_QWORD qword_6500FF10[30] =
{
  1694558182LL,
  7224372056886870034LL,
  1024LL,
  7224376661091814453LL,
  3221225472511LL,
  1694558199LL,
  7224372056886870034LL,
  16LL,
  7224372228685564981LL,
  3221225472511LL,
  1694559881LL,
  7224372056886870034LL,
  8LL,
  7224372297405041717LL,
  3221225472511LL,
  1694558135LL,
  7224372056886870034LL,
  32LL,
  7224372366124518453LL,
  3221225472511LL,
  1694558128LL,
  7224372056886870034LL,
  2048LL,
  7224372434843995189LL,
  3221225472511LL,
  1694558143LL,
  7224372056886870034LL,
  64LL,
  7224372503563475050LL,
  4939212390655LL
}; // weak
_UNKNOWN unk_65010000; // weak
_UNKNOWN unk_650101E0; // weak
_UNKNOWN unk_650101F0; // weak
__int64 qword_65010208 = 65535LL; // weak
unsigned __int64 qword_65010210[153] =
{
  4660uLL,
  0uLL,
  23156025314855251uLL,
  0uLL,
  63520uLL,
  512uLL,
  1112uLL,
  64128uLL,
  0uLL,
  0uLL,
  0uLL,
  0uLL,
  0uLL,
  0uLL,
  17592186044417uLL,
  4055501218334638336uLL,
  13550462159018600008uLL,
  14665890619830124151uLL,
  6338829112145115387uLL,
  9860392450861785758uLL,
  13904826729713452937uLL,
  7206559768378997071uLL,
  8887824083815981774uLL,
  2697125594012062321uLL,
  17977411426259982883uLL,
  17518240381133520331uLL,
  11856364980935821474uLL,
  17149673833929567655uLL,
  3850998104903173617uLL,
  15389560094811667836uLL,
  6889124548868717661uLL,
  4517674189126545267uLL,
  15490419798081424571uLL,
  15146991067320722848uLL,
  1987047169044679641uLL,
  6436153776849164827uLL,
  6349501612384504728uLL,
  15290194280678546581uLL,
  6162397132984860719uLL,
  9666857914062250791uLL,
  11933815763983474023uLL,
  11570172840345526753uLL,
  7857598207555915945uLL,
  9022300725898130729uLL,
  10185546976395491867uLL,
  6023366961035792191uLL,
  17979292017210486574uLL,
  17193369477983325104uLL,
  6280765806413769897uLL,
  6061825339031288128uLL,
  18048057288466096018uLL,
  13307329385264761874uLL,
  3809777201926784272uLL,
  12490894395110119221uLL,
  1995806548424578491uLL,
  2598360618115057128uLL,
  12277113718137490332uLL,
  1152920959564482479uLL,
  17608807484898371284uLL,
  12864065221489694201uLL,
  7869326706884253172uLL,
  5865813518787785209uLL,
  13024578160073853217uLL,
  12312722530565360616uLL,
  12901924884135773110uLL,
  3111737304138494723uLL,
  14394728586785689066uLL,
  4901413384343265186uLL,
  8513062246853628782uLL,
  18167146186449944706uLL,
  1788656101849690819uLL,
  6817936335948041485uLL,
  13914577367267421014uLL,
  13940598428487023775uLL,
  12093790887814030491uLL,
  6202460136314250946uLL,
  9985759556340788377uLL,
  14235085737260164943uLL,
  12338188213533459749uLL,
  5557255152809336305uLL,
  3609393040474983753uLL,
  7431548078474050332uLL,
  2479065961032688161uLL,
  7795735614795479014uLL,
  12039247227563686779uLL,
  7730159328786028625uLL,
  12010208271499147642uLL,
  7229507990uLL,
  0uLL,
  5295828232235654722uLL,
  9053578856508339612uLL,
  1933684119449976396uLL,
  12599163989697962967uLL,
  8354544501303737918uLL,
  13822504042889766958uLL,
  13498961552320531535uLL,
  18340811677059981271uLL,
  42719140348967597uLL,
  12743778724388469156uLL,
  2701434940333960998uLL,
  9425782188220291084uLL,
  14889242691439221656uLL,
  2053082752553363394uLL,
  9379053039831918126uLL,
  10529256697806999566uLL,
  6705774333633518700uLL,
  9884441351446229777uLL,
  11728030046894880374uLL,
  10210890747719984292uLL,
  4901638234713512559uLL,
  7691900693000254681uLL,
  18014268034884338992uLL,
  8392471185892179982uLL,
  28955943472793429uLL,
  15345644629672872057uLL,
  6924819456162047044uLL,
  5082543019332388674uLL,
  18181949856580983453uLL,
  6246048590461618412uLL,
  12776733855835259997uLL,
  13089074086078712142uLL,
  2947321271677774016uLL,
  13121698802475969953uLL,
  2600647563891061166uLL,
  15806080884476967590uLL,
  15756803011780819039uLL,
  18209154338296842362uLL,
  16022892918863361233uLL,
  3315751969241795236uLL,
  7668535009662034554uLL,
  6985837802362843294uLL,
  13217509465579129415uLL,
  14138542670042678744uLL,
  16920344342659726307uLL,
  8506448873567278115uLL,
  15650458965231630130uLL,
  2574626489983756109uLL,
  11296362021117593746uLL,
  11341948896397278117uLL,
  6528638073918609044uLL,
  13130143101538137349uLL,
  12685339614946447632uLL,
  10506040758364866554uLL,
  7501040770272142967uLL,
  1922318397264996220uLL,
  10730817664007136728uLL,
  1956834537814678398uLL,
  9171535369338402062uLL,
  11763792643434974712uLL,
  11163967632222543782uLL,
  5179380771682104722uLL,
  10213732301364740045uLL,
  3123895478097209856uLL
}; // weak


//----- (0000000065000AD0) ----------------------------------------------------
__int64 sub_65000AD0()
{
  return MEMORY[0x65011809];
}

//----- (0000000065000AE4) ----------------------------------------------------
__int64 __fastcall sub_65000AE4(__int64 n258)
{
  MEMORY[0x82000000] = 1128615510;
  MEMORY[0x82000004] = n258;
  MEMORY[0x82000008] = 0LL;
  if ( (_DWORD)n258 == 258 )
  {
    n258 = sub_65000AD0();
    MEMORY[0x8200001C] = n258;
  }
  else
  {
    MEMORY[0x8200001C] = 0;
  }
  return n258;
}

//----- (0000000065000B2C) ----------------------------------------------------
__int64 sub_65000B2C()
{
  MEMORY[0x20010034] = MEMORY[0x20010034] & 0xFFFFFFFC | 3;
  MEMORY[0x64940E0C] = MEMORY[0x64940E0C] & 0xFFE7FFFF | 0x80000;
  MEMORY[0x64920028] = MEMORY[0x64920028] & 0xFFFFFFF8 | 7;
  MEMORY[0x20010028] = MEMORY[0x20010028] & 0xFFFFFFFC | 3;
  MEMORY[0x649202E0] = MEMORY[0x649202E0] & 0xFFFFFFFE | 1;
  sub_65008B84((__int64)&unk_6500F210, 0x77541880uLL);
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFFFFFFF8 | 4;
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFFFFFF8F | 0x40;
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFFFFF8FF | 0x400;
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFFFF8FFF | 0x4000;
  sub_65008B84((__int64)&unk_6500F250, 0x83B9EC80uLL);
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFFF8FFFF | 0x40000;
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xFF8FFFFF | 0x400000;
  MEMORY[0x64940E08] = MEMORY[0x64940E08] & 0xF8FFFFFF | 0x4000000;
  sub_65008B84((__int64)&unk_6500F190, 0x83B9EC80uLL);
  MEMORY[0x64940E08] |= 0x30000000u;
  MEMORY[0x64940E0C] |= 0x1000000u;
  MEMORY[0x64940E0C] = MEMORY[0x64940E0C] & 0xFFFFFFF8 | 3;
  MEMORY[0x20010040] = MEMORY[0x20010040] & 0xFFFFFFF8 | 4;
  MEMORY[0x6492034C] = MEMORY[0x6492034C] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920358] = MEMORY[0x64920358] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920364] = MEMORY[0x64920364] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920370] = MEMORY[0x64920370] & 0xFFFFFFF8 | 1;
  MEMORY[0x6492037C] = MEMORY[0x6492037C] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920388] = MEMORY[0x64920388] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920394] = MEMORY[0x64920394] & 0xFFFFFFF8 | 1;
  MEMORY[0x649203A0] = MEMORY[0x649203A0] & 0xFFFFFFF8 | 1;
  MEMORY[0x649203AC] = MEMORY[0x649203AC] & 0xFFFFFFF8 | 1;
  MEMORY[0x649203B8] = MEMORY[0x649203B8] & 0xFFFFFFFC | 1;
  MEMORY[0x649203C4] = MEMORY[0x649203C4] & 0xFFFFFFFC | 1;
  MEMORY[0x649203D0] = MEMORY[0x649203D0] & 0xFFFFFFFC | 1;
  MEMORY[0x649203DC] = MEMORY[0x649203DC] & 0xFFFFFFF8 | 1;
  MEMORY[0x649203E8] = MEMORY[0x649203E8] & 0xFFFFFFFC | 1;
  MEMORY[0x649203F4] = MEMORY[0x649203F4] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920400] = MEMORY[0x64920400] & 0xFFFFFFFC | 1;
  MEMORY[0x6492040C] = MEMORY[0x6492040C] & 0xFFFFFFF8 | 1;
  MEMORY[0x64920418] = MEMORY[0x64920418] & 0xFFFFFFF8 | 3;
  MEMORY[0x64920424] = MEMORY[0x64920424] & 0xFFFFFFF8 | 3;
  MEMORY[0x64920430] = MEMORY[0x64920430] & 0xFFFFFFF8 | 3;
  MEMORY[0x31800000] |= 0x20u;
  MEMORY[0x25010028] = MEMORY[0x25010028] & 0xFFFFFFFC | 3;
  MEMORY[0x2501004C] |= 1u;
  MEMORY[0x25010064] = MEMORY[0x25010064] & 0xFFFFFFFE | 1;
  MEMORY[0x649202F8] = MEMORY[0x649202F8] & 0xFFFFFFF8 | 5;
  sub_65008B84((__int64)&unk_6500F1D0, 0x493E0000uLL);
  return 0LL;
}

//----- (0000000065001BE0) ----------------------------------------------------
__int64 __fastcall sub_65001BE0(__int64 a1, __int64 a2, _QWORD *a3)
{
  if ( *(_BYTE *)(a1 + 8) != 5 )
    return 0xFFFFFFFFLL;
  if ( (unsigned int)sub_65001CE8(a1, a2, a3) )
    return 0xFFFFFFFFLL;
  return 0LL;
}

//----- (0000000065001C10) ----------------------------------------------------
__int64 __fastcall sub_65001C10(__int64 a1, __int64 a2, int a3, __int64 a4)
{
  if ( a1
    && a2
    && (*(__int64 (__fastcall **)(_QWORD, _QWORD, __int64))(a1 + 112))(
         *(unsigned int *)(a1 + 4),
         *(_QWORD *)(a2 + 72) + a3,
         1LL) == 1 )
  {
    return a4;
  }
  else
  {
    return 0LL;
  }
}

//----- (0000000065001C60) ----------------------------------------------------
bool __fastcall sub_65001C60(__int64 a1, __int64 a2, _QWORD *a3)
{
  _BOOL8 result; // x0

  if ( !a1
    || !a3
    || (*(__int64 (__fastcall **)(_QWORD, __int64, __int64, _QWORD *))(a1 + 112))(
         *(unsigned int *)(a1 + 4),
         a2,
         1LL,
         a3) != 1 )
  {
    return 0LL;
  }
  result = 0LL;
  if ( *a3 == 0x5452415020494645LL )
    return a3[3] == a2;
  return result;
}

//----- (0000000065001CE8) ----------------------------------------------------
__int64 __fastcall sub_65001CE8(__int64 a1, __int64 a2, _QWORD *a3)
{
  __int64 result; // x0
  int v7; // w22
  unsigned int i; // w21
  __int64 n36; // x3
  __int64 v10; // x1
  __int64 v11; // x2
  _BYTE *v12; // [xsp+40h] [xbp+40h]
  _BYTE v13[40]; // [xsp+48h] [xbp+48h] BYREF
  _QWORD v14[64]; // [xsp+70h] [xbp+70h] BYREF
  _BYTE v15[512]; // [xsp+270h] [xbp+270h] BYREF

  if ( a1 && a3 && a2 && (sub_65001C60(a1, 1LL, v14) || sub_65001C60(a1, *(_QWORD *)(a1 + 16) - 1LL, v14)) )
  {
    v7 = 0;
    while ( sub_65001C10(a1, (__int64)v14, v7, (__int64)v15) )
    {
      for ( i = 0; *(_QWORD *)(a1 + 24) >> 7 > (unsigned __int64)i; ++i )
      {
        n36 = 0LL;
        v12 = &v15[128 * (unsigned __int64)(i & 0x1FFFFFF)];
        do
        {
          v13[n36] = *(_WORD *)&v15[128 * (unsigned __int64)(i & 0x1FFFFFF) + 56 + 2 * n36];
          ++n36;
        }
        while ( n36 != 36 );
        result = sub_65002078((__int64)v13, a2);
        if ( !(_DWORD)result )
        {
          v10 = *((_QWORD *)v12 + 4);
          v11 = *((_QWORD *)v12 + 5);
          *a3 = v10;
          a3[1] = v11 + 1 - v10;
          a3[2] = *(_QWORD *)(a1 + 24);
          return result;
        }
      }
      if ( ++v7 == 48 )
        return 0xFFFFFFFFLL;
    }
  }
  return 0xFFFFFFFFLL;
}

//----- (0000000065001E28) ----------------------------------------------------
// write access to const memory has been detected, the output may be wrong!
__int64 __fastcall sub_65001E28(_BYTE *a1)
{
  __int64 v3; // x2
  int v4; // w1
  unsigned __int64 v5; // x4
  unsigned __int64 v6; // x3
  __int64 v7; // x5
  __int64 v8; // x6
  int v9; // w3
  __int64 v10; // x4
  __int64 v11; // x3

  if ( !(unsigned int)sub_6500AAE4((__int64)"misc", 0x820u, 3036676096LL) )
  {
    if ( MEMORY[0xB5000804] != 1111573314 || MEMORY[0xB5000808] > 1u )
    {
      dword_6500F290 = 0;
      return 0LL;
    }
    if ( (MEMORY[0xB5000809] & 7u) > 4 )
      MEMORY[0xB5000809] = MEMORY[0xB5000809] & 0xF8 | 4;
    v3 = 0LL;
    MEMORY[0x65010E18] = MEMORY[0xB500080B];
    v4 = -1;
    while ( 1 )
    {
      if ( (int)v3 >= (MEMORY[0xB5000809] & 7) )
      {
        *a1 = *(_BYTE *)(2LL * v4 + 0xB500080CLL) >> 7;
        dword_6500F290 = v4;
        return (unsigned int)v4;
      }
      if ( (*(_BYTE *)(2LL * (int)v3 + 0xB500080DLL) & 1) == 0
        && ((*(_BYTE *)(2LL * (int)v3 + 0xB500080CLL) >> 4) & 7) != 0 )
      {
        if ( v4 == -1 )
        {
          v4 = v3;
        }
        else
        {
          v5 = *(unsigned __int8 *)(2 * (v3 + 1518339078));
          v6 = *(unsigned __int8 *)(2LL * v4 + 0xB500080CLL);
          LODWORD(v7) = v5 & 0xF;
          LODWORD(v8) = v6 & 0xF;
          if ( (_DWORD)v7 == (_DWORD)v8 && (v7 = (v5 >> 7) & 1, v8 = (v6 >> 7) & 1, (_DWORD)v7 == (_DWORD)v8) )
          {
            v10 = (v5 >> 4) & 7;
            v11 = (v6 >> 4) & 7;
            if ( (_DWORD)v10 == (_DWORD)v11 )
              goto LABEL_20;
            v9 = v11 - v10;
          }
          else
          {
            v9 = v8 - v7;
          }
          if ( v9 < 0 )
            v4 = v3;
        }
      }
LABEL_20:
      ++v3;
    }
  }
  return 0xFFFFFFFFLL;
}
// 65001E74: write access to const memory at 6500F290 has been detected
// 65001FCC: write access to const memory at 6500F290 has been detected
// 6500F290: using guessed type int dword_6500F290;

//----- (0000000065001FE8) ----------------------------------------------------
__int64 sub_65001FE8()
{
  return MEMORY[0x65010E18];
}

//----- (0000000065002000) ----------------------------------------------------
__int64 __fastcall sub_65002000(__int64 result, char a2, __int64 i_1)
{
  __int64 i; // x3

  for ( i = 0LL; i != i_1; ++i )
    *(_BYTE *)(result + i) = a2;
  return result;
}

//----- (000000006500201C) ----------------------------------------------------
__int64 __fastcall sub_6500201C(__int64 result, __int64 a2, __int64 i_1)
{
  __int64 v3; // x3
  __int64 v4; // x4
  __int64 i; // x3

  v3 = ((unsigned __int8)result | (unsigned __int8)a2) & 7;
  v4 = result;
  if ( (((unsigned __int8)result | (unsigned __int8)a2) & 7) == 0 )
  {
    while ( (unsigned __int64)(i_1 - v3) > 7 )
    {
      *(_QWORD *)(result + v3) = *(_QWORD *)(a2 + v3);
      v3 += 8LL;
    }
    v4 = result + (i_1 & 0xFFFFFFFFFFFFFFF8LL);
    a2 += i_1 & 0xFFFFFFFFFFFFFFF8LL;
    i_1 &= 7u;
  }
  for ( i = 0LL; i != i_1; ++i )
    *(_BYTE *)(v4 + i) = *(_BYTE *)(a2 + i);
  return result;
}

//----- (0000000065002078) ----------------------------------------------------
__int64 __fastcall sub_65002078(__int64 a1, __int64 a2)
{
  __int64 v3; // x2
  int v4; // w3
  __int64 result; // x0

  v3 = 0LL;
  do
  {
    v4 = *(unsigned __int8 *)(a1 + v3);
    result = (unsigned int)(char)(v4 - *(_BYTE *)(a2 + v3));
    if ( (_BYTE)v4 != *(_BYTE *)(a2 + v3) )
      break;
    ++v3;
  }
  while ( v4 );
  return result;
}

//----- (00000000650020A0) ----------------------------------------------------
__int64 __fastcall sub_650020A0(__int64 result, char a2, char a3, int a4)
{
  result = (unsigned int)result;
  *(_DWORD *)(unsigned int)result = *(_DWORD *)(unsigned int)result & ~(((1 << a3) - 1) << a2) | ((((1 << a3) - 1) & a4) << a2);
  return result;
}

//----- (00000000650020D0) ----------------------------------------------------
__int64 __fastcall sub_650020D0(unsigned int a1, char a2, char a3)
{
  return ((unsigned int)(((1 << a3) - 1) << a2) & *(_DWORD *)a1) >> a2;
}

//----- (00000000650020F4) ----------------------------------------------------
__int64 __fastcall sub_650020F4(__int64 i_1)
{
  unsigned int i; // [xsp+4h] [xbp-Ch]
  unsigned int j; // [xsp+8h] [xbp-8h]

  for ( i = 0; i < (unsigned int)i_1; ++i )
  {
    for ( j = 0; j <= 6; ++j )
      ;
  }
  return i_1;
}

//----- (000000006500215C) ----------------------------------------------------
__int64 __fastcall sub_6500215C(__int64 i_1)
{
  unsigned int i; // [xsp+8h] [xbp-8h]

  for ( i = 0; i < (unsigned int)i_1; ++i )
    ;
  return i_1;
}

//----- (00000000650021A0) ----------------------------------------------------
_BYTE *__fastcall sub_650021A0(_BYTE *result)
{
  while ( *result )
  {
    while ( MEMORY[0x2021000D] )
      ;
    MEMORY[0x20210000] = (unsigned __int8)*result++;
  }
  return result;
}

//----- (00000000650021D0) ----------------------------------------------------
_BYTE *__fastcall sub_650021D0(unsigned int a1)
{
  unsigned int v1; // w19
  unsigned int v2; // w0
  int i; // w1
  int v4; // w2
  int i_1; // w6

  v1 = a1;
  if ( (a1 & 0x80000000) != 0 )
  {
    v1 = -a1;
    sub_650021A0("-");
  }
  v2 = v1;
  for ( i = 0; ; ++i )
  {
    v2 /= 0xAu;
    v4 = i + 1;
    if ( !v2 )
      break;
  }
  i_1 = i;
  do
  {
    *(_BYTE *)(0x65010E20LL + i--) = v1 % 0xA + 48;
    v1 /= 0xAu;
  }
  while ( i != -1 );
  *(_BYTE *)(0x65010E20LL + v4) = 32;
  *(_BYTE *)(0x65010E20LL + i_1 + 2) = 0;
  return sub_650021A0((_BYTE *)0x65010E20);
}

//----- (0000000065002264) ----------------------------------------------------
__int64 sub_65002264()
{
  int *v0; // x20
  unsigned int n1610612768; // w19
  unsigned int v2; // w21
  int v3; // w3
  __int64 n1610612768_1; // x0
  __int64 n1610612832; // x0

  v0 = &dword_6500E4F0;
  n1610612768 = 1610612768;
  do
  {
    v2 = n1610612768 + 4;
    sub_650020A0(n1610612768, 24, 8, *v0);
    v3 = v0[1];
    v0 += 4;
    sub_650020A0(n1610612768, 16, 8, v3);
    sub_650020A0(n1610612768, 0, 4, *(v0 - 2));
    sub_650020A0(n1610612768 + 4, 0, 4, *(v0 - 1));
    n1610612768_1 = n1610612768;
    n1610612768 += 8;
    sub_650020A0(n1610612768_1, 8, 1, 0);
    sub_650020A0(v2, 8, 1, 0);
    n1610612832 = 1610612832LL;
  }
  while ( n1610612768 != 1610612832 );
  return n1610612832;
}
// 6500E4F0: using guessed type int dword_6500E4F0;

//----- (0000000065002328) ----------------------------------------------------
__int64 sub_65002328()
{
  sub_650020A0(1610612756LL, 9, 1, 1);
  sub_650020A0(1610612756LL, 12, 1, 1);
  sub_650020A0(1610612788LL, 7, 1, 1);
  sub_650020A0(1610613112LL, 0, 6, 40);
  sub_650020A0(1610613112LL, 8, 6, 56);
  sub_650020A0(1610613112LL, 16, 6, 40);
  sub_650020A0(1610613112LL, 24, 6, 56);
  sub_650020A0(1610613116LL, 0, 6, 3);
  sub_650020A0(1610613116LL, 8, 6, 2);
  sub_650020A0(1610613116LL, 16, 6, 4);
  return sub_650020A0(1610613116LL, 24, 6, 2);
}

//----- (000000006500243C) ----------------------------------------------------
__int64 sub_6500243C()
{
  sub_650020A0(1610953160LL, 0, 1, 1);
  MEMORY[0x60053200] = -1966315930;
  MEMORY[0x60053204] = 1726925977;
  sub_650020A0(1610973184LL, 0, 16, 3084);
  MEMORY[0x600820E0] = 3;
  MEMORY[0x600820E4] = 17891601;
  MEMORY[0x60082160] = 3;
  MEMORY[0x60082164] = 161024409;
  MEMORY[0x60085060] = 3;
  MEMORY[0x60085064] = 107349606;
  MEMORY[0x600852E0] = 3;
  MEMORY[0x600852E4] = 214699212;
  MEMORY[0x60085360] = 3;
  MEMORY[0x60085364] = 250482414;
  sub_650020A0(1610953208LL, 16, 4, 12);
  sub_650020A0(1610953208LL, 20, 4, 10);
  sub_650020A0(1610953208LL, 24, 4, 8);
  return sub_650020A0(1610953208LL, 28, 4, 8);
}

//----- (00000000650025A4) ----------------------------------------------------
__int64 sub_650025A4()
{
  sub_650020A0(1610612752LL, 0, 20, 589825);
  sub_650020A0(1610612772LL, 8, 1, 0);
  return sub_650020A0(1610612796LL, 8, 1, 0);
}

//----- (00000000650025FC) ----------------------------------------------------
__int64 sub_650025FC()
{
  sub_65002264();
  sub_65002328();
  sub_6500243C();
  return sub_650025A4();
}

//----- (0000000065002618) ----------------------------------------------------
__int64 sub_65002618()
{
  __int64 n1610970368; // x0

  MEMORY[0x6005500C] = 16777217;
  MEMORY[0x60058500] = 16777217;
  MEMORY[0x60055104] = 65280;
  MEMORY[0x60055000] = 114560;
  MEMORY[0x600551D8] = 15;
  sub_650020A0(1611202628LL, 0, 12, 2);
  sub_650020A0(1610956828LL, 16, 16, 514);
  sub_650020A0(1610956828LL, 0, 8, 2);
  n1610970368 = 1610970368LL;
  MEMORY[0x60057500] = 1;
  return n1610970368;
}

//----- (00000000650026DC) ----------------------------------------------------
__int64 sub_650026DC()
{
  sub_650020A0(1610612756LL, 0, 3, 1);
  sub_650020A0(1610613060LL, 24, 3, 1);
  sub_650020A0(1610613064LL, 0, 15, 0);
  sub_650020A0(1610613064LL, 16, 15, 0);
  sub_650020A0(1610613068LL, 0, 15, 0);
  sub_650020A0(1610613068LL, 16, 15, 0);
  sub_650020A0(1610613072LL, 0, 15, 0);
  return sub_650020A0(1610613072LL, 16, 15, 0);
}

//----- (00000000650027A8) ----------------------------------------------------
__int64 __fastcall sub_650027A8(int a1)
{
  int n64; // w0

  switch ( a1 )
  {
    case 0:
      n64 = 64;
      goto LABEL_7;
    case 1:
      n64 = 128;
      goto LABEL_7;
    case 2:
      n64 = 256;
      goto LABEL_7;
    case 3:
      n64 = 512;
      goto LABEL_7;
    case 4:
      n64 = 1024;
LABEL_7:
      MEMORY[0x65010E64] = n64;
      break;
    default:
      return sub_650020A0(1610612756LL, 0, 3, a1);
  }
  return sub_650020A0(1610612756LL, 0, 3, a1);
}

//----- (0000000065002810) ----------------------------------------------------
__int64 __fastcall sub_65002810(unsigned int a1, unsigned int a2)
{
  char *_r_ndelat_N___4; // x0

  if ( a1 <= a2 )
  {
    if ( a2 - a1 <= 3 )
      return (a1 + a2) >> 1;
    _r_ndelat_N___4 = "\r\ndelat N > 4";
  }
  else
  {
    if ( a1 - a2 <= 3 )
      return (a1 + a2) >> 1;
    _r_ndelat_N___4 = "\r\ndelat P > 4";
  }
  sub_650021A0(_r_ndelat_N___4);
  return 0xFFFFFFFFLL;
}

//----- (0000000065002868) ----------------------------------------------------
__int64 sub_65002868()
{
  sub_650020A0(1680900100LL, 3, 4, 0);
  sub_650020A0(1680900116LL, 0, 3, 0);
  sub_650020A0(1680900132LL, 3, 4, 0);
  sub_650020A0(1680900148LL, 0, 3, 0);
  sub_650020A0(1680900164LL, 3, 4, 0);
  return sub_650020A0(1680900180LL, 0, 3, 0);
}

//----- (0000000065002904) ----------------------------------------------------
__int64 __fastcall sub_65002904(__int64 n3, _DWORD *a2)
{
  if ( (_DWORD)n3 == 1066 )
  {
    n3 = 3LL;
  }
  else if ( (unsigned int)n3 > 0x42A )
  {
    if ( (_DWORD)n3 == 1536 )
    {
      n3 = 5LL;
    }
    else if ( (unsigned int)n3 > 0x600 )
    {
      if ( (_DWORD)n3 == 1866 )
      {
        n3 = 6LL;
      }
      else
      {
        if ( (_DWORD)n3 != 2133 )
          goto LABEL_21;
        n3 = 7LL;
      }
    }
    else
    {
      if ( (_DWORD)n3 != 1244 )
        goto LABEL_21;
      n3 = 4LL;
    }
  }
  else
  {
    switch ( (_DWORD)n3 )
    {
      case 0x215:
        n3 = 1LL;
        break;
      case 0x300:
        n3 = 2LL;
        break;
      case 0x180:
        *a2 = 0;
        return n3;
      default:
        while ( 1 )
LABEL_21:
          ;
    }
  }
  *a2 = n3;
  return n3;
}

//----- (000000006500299C) ----------------------------------------------------
__int64 sub_6500299C()
{
  return sub_650020A0(1610953732LL, 2, 1, 1);
}

//----- (00000000650029B4) ----------------------------------------------------
__int64 sub_650029B4()
{
  return sub_650020A0(1610953732LL, 2, 1, 0);
}

//----- (00000000650029CC) ----------------------------------------------------
__int64 sub_650029CC()
{
  return sub_650020A0(1610953732LL, 0, 1, 1);
}

//----- (00000000650029E4) ----------------------------------------------------
__int64 sub_650029E4()
{
  return sub_650020A0(1610953732LL, 0, 1, 0);
}

//----- (00000000650029FC) ----------------------------------------------------
__int64 sub_650029FC()
{
  sub_650020A0(1610616836LL, 9, 1, 1);
  sub_650020A0(1610620932LL, 9, 1, 1);
  sub_650020A0(1610616836LL, 9, 1, 0);
  return sub_650020A0(1610620932LL, 9, 1, 0);
}

//----- (0000000065002A68) ----------------------------------------------------
__int64 __fastcall sub_65002A68(unsigned int a1)
{
  __int64 v1; // x19

  v1 = 44LL * a1;
  sub_650020A0(1611202584LL, 0, 2, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 4));
  sub_650020A0(1611202584LL, 2, 3, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 8));
  sub_650020A0(1611202584LL, 5, 3, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 12));
  sub_650020A0(1611202584LL, 8, 3, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 16));
  sub_650020A0(1611202584LL, 11, 3, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 20));
  sub_650020A0(1611202584LL, 16, 4, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 24));
  sub_650020A0(1611202584LL, 20, 2, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 28));
  return sub_650020A0(1611202584LL, 22, 3, *(_DWORD *)(MEMORY[0x65010E90] + v1 + 32));
}

//----- (0000000065002B8C) ----------------------------------------------------
__int64 __fastcall sub_65002B8C(unsigned int a1)
{
  sub_65002A68(a1);
  sub_650020F4(1LL);
  sub_650020A0(1610613036LL, 4, 3, a1);
  sub_650020F4(1LL);
  return sub_650020A0(1610613036LL, 4, 3, a1);
}

//----- (0000000065002BE8) ----------------------------------------------------
__int64 sub_65002BE8()
{
  sub_650020A0(1610612736LL, 8, 1, 0);
  return sub_650020A0(1610613028LL, 12, 1, 0);
}

//----- (0000000065002C20) ----------------------------------------------------
__int64 sub_65002C20()
{
  sub_650020A0(1610612992LL, 8, 1, 0);
  sub_650020A0(1610612992LL, 0, 1, 0);
  sub_650020A0(1610612736LL, 4, 3, 4);
  sub_650020A0(1610612736LL, 29, 1, 1);
  sub_650020A0(1610612992LL, 20, 4, 3);
  sub_650020A0(1610612736LL, 0, 3, 6);
  sub_650020A0(1610612992LL, 4, 3, 2);
  sub_650020A0(1610612992LL, 11, 2, 3);
  sub_650020A0(1610613060LL, 0, 4, 3);
  sub_650020A0(1610613060LL, 27, 1, 0);
  sub_650020A0(1610613036LL, 12, 4, 5);
  sub_650026DC();
  sub_650027A8(1);
  sub_650025FC();
  sub_650020A0(1610613116LL, 0, 6, 8);
  sub_650020A0(1610613116LL, 8, 6, 4);
  sub_650020A0(1610613116LL, 16, 6, 12);
  return sub_650020A0(1610613116LL, 24, 6, 4);
}

//----- (0000000065002D98) ----------------------------------------------------
__int64 sub_65002D98()
{
  __int64 result; // x0
  int *v1; // x3
  int i; // w1
  unsigned __int64 v3; // x4
  int v4; // t1

  MEMORY[0x65010E50] = &unk_6500F348;
  for ( result = 0LL; (unsigned int)result < MEMORY[0x65010E68]; result = (unsigned int)(result + 1) )
  {
    v1 = (int *)((char *)&unk_6500F348 + 96 * (unsigned int)result + 16);
    for ( i = 0; i != 80; i += 4 )
    {
      v3 = (unsigned int)(96 * result + 1610613120 + i);
      v4 = *v1++;
      *(_DWORD *)v3 = v4;
    }
  }
  return result;
}

//----- (0000000065002E10) ----------------------------------------------------
__int64 sub_65002E10()
{
  unsigned int i; // w0
  unsigned int v1; // w1
  int *v2; // x3
  int n46; // w2
  int v4; // t1
  __int64 result; // x0
  unsigned __int64 v6; // x1

  MEMORY[0x65010E48] = &unk_6500F848;
  for ( i = 0; i < MEMORY[0x65010E68]; ++i )
  {
    v1 = 192 * i + 1610616896;
    v2 = (int *)((char *)&unk_6500F848 + 200 * i + 8);
    n46 = 0;
    do
    {
      ++n46;
      *(_DWORD *)v1 = *v2;
      v4 = *v2++;
      *(_DWORD *)(v1 + 4096) = v4;
      v1 += 4;
    }
    while ( n46 != 46 );
  }
  result = 0LL;
  while ( (unsigned int)result < MEMORY[0x65010E68] )
  {
    v6 = (unsigned int)(192 * result + 1610617084);
    result = (unsigned int)(result + 1);
    *(_DWORD *)v6 = 106;
  }
  return result;
}

//----- (0000000065002ED0) ----------------------------------------------------
__int64 sub_65002ED0()
{
  sub_650020A0(1610616836LL, 18, 1, 1);
  return sub_650020A0(1610620932LL, 18, 1, 1);
}

//----- (0000000065002F0C) ----------------------------------------------------
__int64 sub_65002F0C()
{
  return sub_65002F14(0x65010E58uLL);
}

//----- (0000000065002F14) ----------------------------------------------------
__int64 __fastcall sub_65002F14(unsigned int *a1)
{
  __int64 n6; // x0
  __int64 v2; // x2
  __int64 i; // x1
  unsigned int v4; // w3

  n6 = *a1;
  if ( (_DWORD)n6 == 64 || (_DWORD)n6 == 65 )
  {
    sub_65008028(7);
    n6 = 6LL;
    v2 = MEMORY[0x65010E48];
    for ( i = 1610617076LL; i != 1610618612; *(_DWORD *)(i + 3904) = *(_DWORD *)(v2 - 12) )
    {
      i += 192LL;
      v4 = *(_DWORD *)(v2 + 188) & 0xFFFFFF88 | 0x66;
      *(_DWORD *)(v2 + 188) = v4;
      *(_DWORD *)(i - 192) = v4;
      v2 += 200LL;
    }
  }
  return n6;
}

//----- (0000000065002F4C) ----------------------------------------------------
__int64 sub_65002F4C()
{
  __int64 v0; // x5
  __int64 result; // x0
  __int64 v2; // x3
  unsigned int v3; // w2
  unsigned int *v4; // x3

  v0 = MEMORY[0x65010E50];
  result = 0LL;
  while ( (unsigned int)result < MEMORY[0x65010E68] )
  {
    v2 = v0 + 96LL * (unsigned int)result;
    v3 = *(_DWORD *)(v2 + 32) & 0xFFFFCFFF;
    *(_DWORD *)(v2 + 32) = v3;
    v4 = (unsigned int *)(unsigned int)(96 * result + 1610613136);
    result = (unsigned int)(result + 1);
    *v4 = v3;
  }
  return result;
}

//----- (0000000065002FA4) ----------------------------------------------------
__int64 sub_65002FA4()
{
  sub_650020A0(1610613004LL, 15, 1, 1);
  return sub_650020A0(1610613004LL, 12, 1, 1);
}

//----- (0000000065002FE0) ----------------------------------------------------
__int64 sub_65002FE0()
{
  MEMORY[0x60001640] = 974226433;
  MEMORY[0x600016C0] = 974226433;
  MEMORY[0x60001700] = 974226433;
  MEMORY[0x60002640] = 974226433;
  MEMORY[0x600026C0] = 974226433;
  MEMORY[0x60002700] = 974226433;
  sub_650029E4();
  sub_650020A0(1610612748LL, 0, 4, 15);
  while ( (HIWORD(MEMORY[0x6000000C]) & 0xF) != 0xF )
    ;
  return sub_650020A0(1610612748LL, 0, 4, 0);
}

//----- (00000000650030B0) ----------------------------------------------------
unsigned __int64 __fastcall sub_650030B0(int a1, int n2, int a3)
{
  char n28; // w1
  int v6; // w3
  unsigned __int64 result; // x0

  sub_650020A0(1610613000LL, 0, 8, a3);
  sub_650020A0(1610612996LL, 0, 16, a1);
  if ( n2 == 1 )
  {
    sub_650020A0(1610612996LL, 31, 1, 0);
    n28 = 28;
LABEL_7:
    v6 = 1;
    goto LABEL_8;
  }
  if ( n2 )
  {
    if ( n2 != 2 )
    {
      while ( 1 )
        ;
    }
    n28 = 31;
    goto LABEL_7;
  }
  sub_650020A0(1610612996LL, 31, 1, 0);
  n28 = 28;
  v6 = 0;
LABEL_8:
  sub_650020A0(1610612996LL, n28, 1, v6);
  sub_650020A0(1610612996LL, 24, 1, 1);
  do
    result = ((unsigned __int64)MEMORY[0x60000104] >> 19) & 0x1FF;
  while ( (_DWORD)result );
  return result;
}

//----- (00000000650031BC) ----------------------------------------------------
__int64 sub_650031BC()
{
  int v0; // w4
  int n65; // w5
  int v2; // w1
  __int64 v3; // x0
  int v4; // w0
  int v5; // w21
  int v6; // w20
  int n128; // w2
  __int64 v8; // x2
  int n65_1; // w6
  __int64 result; // x0
  __int64 v11; // x4
  unsigned __int64 v12; // x3

  v0 = MEMORY[0x65010E68];
  n65 = MEMORY[0x65010E58];
  v2 = 0;
  v3 = MEMORY[0x65010E50];
  while ( v2 != v0 )
  {
    if ( n65 != 65 )
      *(_DWORD *)(v3 + 8) |= 0x80u;
    ++v2;
    v3 += 96LL;
  }
  sub_650020A0(1610613004LL, 12, 1, 0);
  v4 = sub_650020D0(0x600004E8u, 31, 1);
  v5 = v4 << 7;
  v6 = v4;
  sub_650030B0(13, 2, (v4 << 7) | 0x40);
  sub_650030B0(3, 2, *(_DWORD *)(MEMORY[0x65010E50] + 8LL));
  sub_650030B0(13, 2, v5);
  sub_650030B0(3, 2, *(_DWORD *)(MEMORY[0x65010E50] + 8LL));
  if ( v6 )
    n128 = 128;
  else
    n128 = 64;
  sub_650030B0(13, 2, n128);
  sub_650020A0(1610613004LL, 12, 1, 1);
  v8 = MEMORY[0x65010E50];
  n65_1 = MEMORY[0x65010E58];
  result = 0LL;
  while ( (unsigned int)result < MEMORY[0x65010E68] )
  {
    if ( n65_1 != 65 )
      *(_DWORD *)(v8 + 96LL * (unsigned int)result + 32) |= 0x1000u;
    v11 = v8 + 96LL * (unsigned int)result;
    v12 = (unsigned int)(96 * result + 1610613136);
    result = (unsigned int)(result + 1);
    *(_DWORD *)v12 = *(_DWORD *)(v11 + 32);
  }
  return result;
}

//----- (0000000065003340) ----------------------------------------------------
__int64 __fastcall sub_65003340(int a1, int a2, _DWORD *a3)
{
  unsigned __int64 v5; // x0
  unsigned __int64 v6; // x2
  unsigned __int64 v7; // x3
  __int64 result; // x0

  sub_650029FC();
  sub_650020A0(1610612996LL, 0, 16, a1);
  sub_650020A0(1610612996LL, 31, 1, 0);
  sub_650020A0(1610612996LL, 28, 1, a2);
  sub_650020A0(1610612996LL, 25, 1, 1);
  while ( ((MEMORY[0x60000104] >> 19) & 0x1FF) != 0 )
    ;
  v5 = ((unsigned __int64)MEMORY[0x600000A4] >> 12) & 1;
  v6 = ((unsigned __int64)MEMORY[0x600000A8] >> 12) & 1;
  v7 = ((unsigned __int64)MEMORY[0x600000AC] >> 12) & 1;
  if ( (MEMORY[0x600000A0] & 0x1000) != 0 )
  {
    *a3 = MEMORY[0x600000B0];
    if ( !(_DWORD)v5 )
      goto LABEL_6;
  }
  else if ( !(_DWORD)v5 )
  {
    return 0xFFFFFFFFLL;
  }
  *a3 |= MEMORY[0x600000B2] << 8;
LABEL_6:
  if ( (_DWORD)v6 )
    *a3 |= MEMORY[0x600000B4] << 16;
  result = 0LL;
  if ( (_DWORD)v7 )
    *a3 |= HIWORD(MEMORY[0x600000B4]) << 24;
  return result;
}

//----- (00000000650034B0) ----------------------------------------------------
unsigned __int64 __fastcall sub_650034B0(int a1, int n2)
{
  char n28; // w1
  int v4; // w3
  unsigned __int64 result; // x0

  sub_650020A0(1610613000LL, 0, 8, a1);
  if ( n2 == 1 )
  {
    sub_650020A0(1610612996LL, 31, 1, 0);
    n28 = 28;
LABEL_7:
    v4 = 1;
    goto LABEL_8;
  }
  if ( n2 )
  {
    if ( n2 != 2 )
    {
      while ( 1 )
        ;
    }
    n28 = 31;
    goto LABEL_7;
  }
  sub_650020A0(1610612996LL, 31, 1, 0);
  n28 = 28;
  v4 = 0;
LABEL_8:
  sub_650020A0(1610612996LL, n28, 1, v4);
  sub_650020A0(1610612996LL, 27, 1, 1);
  do
    result = ((unsigned __int64)MEMORY[0x60000104] >> 19) & 0x1FF;
  while ( (_DWORD)result );
  return result;
}

//----- (00000000650035A0) ----------------------------------------------------
unsigned __int64 __fastcall sub_650035A0(int n2)
{
  unsigned __int64 result; // x0

  sub_650020A0(1610612992LL, 20, 4, n2);
  sub_650034B0(79, 0);
  sub_650020F4(1LL);
  sub_650034B0(81, 0);
  sub_650034B0(79, 1);
  sub_650020F4(1LL);
  result = sub_650034B0(81, 1);
  if ( n2 == 2 )
    return sub_650020A0(1610612992LL, 20, 4, 15);
  return result;
}

//----- (000000006500363C) ----------------------------------------------------
unsigned __int64 __fastcall sub_6500363C(unsigned int a1)
{
  __int64 v2; // x19
  int v3; // w1
  int v4; // w3
  __int64 v5; // x0
  unsigned int v6; // w2

  sub_650020F4(500LL);
  sub_650020A0(1610612992LL, 15, 1, 1);
  sub_650020F4(3000LL);
  sub_650020A0(1610612992LL, 14, 1, 1);
  sub_650020F4(10LL);
  v2 = 96LL * a1;
  sub_650030B0(2, 2, *(_DWORD *)(MEMORY[0x65010E50] + v2 + 4));
  sub_650020F4(2LL);
  sub_650030B0(1, 2, *(_DWORD *)(MEMORY[0x65010E50] + v2));
  sub_650020F4(2LL);
  v3 = 0;
  v4 = MEMORY[0x65010E68];
  v5 = MEMORY[0x65010E50];
  while ( v3 != v4 )
  {
    ++v3;
    v6 = *(_DWORD *)(v5 + 8) & 0xFFFFFFC7 | 0x30;
    v5 += 96LL;
    *(_DWORD *)(v5 - 88) = v6;
  }
  sub_650030B0(3, 2, *(_DWORD *)(MEMORY[0x65010E50] + v2 + 8));
  sub_650030B0(22, 2, *(_DWORD *)(MEMORY[0x65010E50] + v2 + 12));
  sub_650035A0(1);
  return sub_650035A0(2);
}

//----- (000000006500376C) ----------------------------------------------------
unsigned __int64 __fastcall sub_6500376C(unsigned int a1)
{
  sub_650029FC();
  sub_650020A0(1610616836LL, 10, 1, 1);
  sub_650020A0(1610616836LL, 19, 1, 1);
  sub_650020A0(1610620932LL, 10, 1, 1);
  sub_650020A0(1610620932LL, 19, 1, 1);
  return sub_6500363C(a1);
}

//----- (00000000650037F0) ----------------------------------------------------
__int64 sub_650037F0()
{
  sub_650020A0(1610612736LL, 8, 2, 3);
  sub_650020A0(1610613012LL, 24, 1, 0);
  if ( MEMORY[0x65010E6C] == 1 )
    sub_650020A0(1610613012LL, 25, 1, 0);
  sub_650020A0(1610613012LL, 0, 24, 0);
  sub_650020A0(1610613036LL, 0, 4, 15);
  sub_650020A0(1610613036LL, 12, 4, 5);
  sub_650020A0(1610613036LL, 17, 1, 1);
  sub_650020A0(1610613036LL, 11, 1, 0);
  sub_650020A0(1610613016LL, 0, 24, 0);
  sub_650020A0(1610613016LL, 25, 1, 0);
  sub_650020A0(1610613016LL, 26, 2, 0);
  sub_650020A0(1610613016LL, 28, 1, 0);
  sub_650020A0(1610613060LL, 16, 1, 1);
  sub_650020A0(1610613060LL, 4, 12, 25);
  sub_650020A0(1610613028LL, 0, 3, 5);
  sub_650020A0(1610613028LL, 12, 3, 5);
  return sub_650020A0(1610613028LL, 16, 3, 7);
}

//----- (00000000650039A0) ----------------------------------------------------
__int64 sub_650039A0()
{
  sub_650020A0(1611202760LL, 0, 1, 1);
  sub_650020A0(1611202576LL, 0, 1, 1);
  sub_650020A0(1611202576LL, 1, 1, 0);
  sub_650020A0(1611202576LL, 2, 1, 0);
  return sub_650020A0(1687423040LL, 0, 9, 208);
}

//----- (0000000065003A24) ----------------------------------------------------
__int64 sub_65003A24()
{
  unsigned int n1611202592; // w20
  __int64 i; // x19
  __int64 n1611202592_1; // x0
  int v3; // w3
  __int64 result; // x0

  n1611202592 = 1611202592;
  for ( i = 0LL; i != 352; i += 44LL )
  {
    sub_650020A0(n1611202592, 0, 2, *(_DWORD *)(MEMORY[0x65010E90] + i + 4));
    sub_650020A0(n1611202592, 2, 3, *(_DWORD *)(MEMORY[0x65010E90] + i + 8));
    sub_650020A0(n1611202592, 5, 3, *(_DWORD *)(MEMORY[0x65010E90] + i + 12));
    sub_650020A0(n1611202592, 8, 3, *(_DWORD *)(MEMORY[0x65010E90] + i + 16));
    sub_650020A0(n1611202592, 11, 3, *(_DWORD *)(MEMORY[0x65010E90] + i + 20));
    sub_650020A0(n1611202592, 16, 4, *(_DWORD *)(MEMORY[0x65010E90] + i + 24));
    sub_650020A0(n1611202592, 20, 2, *(_DWORD *)(MEMORY[0x65010E90] + i + 28));
    n1611202592_1 = n1611202592;
    v3 = *(_DWORD *)(MEMORY[0x65010E90] + i + 40);
    n1611202592 += 4;
    result = sub_650020A0(n1611202592_1, 22, 3, v3);
  }
  return result;
}

//----- (0000000065003B48) ----------------------------------------------------
__int64 __fastcall sub_65003B48(int a1)
{
  sub_650020A0(1611202624LL, 1, 3, a1);
  sub_650020A0(1611202624LL, 0, 1, 1);
  while ( (MEMORY[0x60090040] & 0x10) == 0 )
    ;
  return sub_650020A0(1611202624LL, 0, 1, 0);
}

//----- (0000000065003BAC) ----------------------------------------------------
__int64 __fastcall sub_65003BAC(__int64 a1, unsigned int n3)
{
  unsigned int v2; // w19
  int v3; // w3
  __int64 n1610964992; // x0

  MEMORY[0x6005503C] = 16;
  MEMORY[0x60055000] = 0x100000;
  v2 = 1;
  sub_65002904(n3, (_DWORD *)0x65010E68);
  sub_650048D4();
  while ( v2 <= MEMORY[0x65010E68] )
  {
    sub_65006FB0(*(_DWORD *)(MEMORY[0x65010E90] + 44LL * v2), v2);
    sub_650020A0(1610613060LL, 16, 1, 1);
    v3 = sub_6500880C(1, 0LL, 0xFF0uLL);
    if ( MEMORY[0x65010E78] )
      v3 += sub_6500880C(
              1,
              MEMORY[0x65010E78] + MEMORY[0x65010E70] - 4096LL,
              MEMORY[0x65010E78] + MEMORY[0x65010E70] - 1LL);
    if ( v3 )
    {
      sub_65002260("train bist failed\n");
      while ( 1 )
        ;
    }
    ++v2;
    sub_650020A0(1610613060LL, 16, 1, 0);
  }
  MEMORY[0x6005603C] = 16;
  n1610964992 = 1610964992LL;
  MEMORY[0x60056000] = 0x100000;
  return n1610964992;
}

//----- (0000000065003CDC) ----------------------------------------------------
__int64 sub_65003CDC()
{
  __int64 n1610952964; // x0

  n1610952964 = 1610952964LL;
  MEMORY[0x60053104] &= 0xFFFF00FF;
  return n1610952964;
}

//----- (0000000065003CFC) ----------------------------------------------------
__int64 sub_65003CFC()
{
  __int64 n1687226556; // x0

  n1687226556 = 1687226556LL;
  MEMORY[0x649108BC] |= 1u;
  return n1687226556;
}

//----- (0000000065003D1C) ----------------------------------------------------
unsigned __int64 __fastcall sub_65003D1C(__int64 n3)
{
  unsigned int n0x65010E68_; // [xsp+1Ch] [xbp+1Ch] BYREF

  sub_65002904(n3, &n0x65010E68_);
  sub_65002BE8();
  sub_650029CC();
  sub_65002E10();
  sub_65002ED0();
  sub_6500299C();
  sub_65002D98();
  sub_65002C20();
  sub_650029B4();
  sub_65002F0C();
  sub_65002B8C(n0x65010E68_);
  sub_65002FE0();
  return sub_6500376C(n0x65010E68_);
}

//----- (0000000065003D6C) ----------------------------------------------------
__int64 sub_65003D6C()
{
  __int64 n1610620924; // x0

  MEMORY[0x6000009C] = 0;
  n1610620924 = 1610620924LL;
  MEMORY[0x60001FFC] = 0;
  return n1610620924;
}

//----- (0000000065003D88) ----------------------------------------------------
__int64 sub_65003D88()
{
  int n15_1; // w20
  int n15; // w19
  int n13; // w21
  int n8_1; // w19
  int n8; // w3

  sub_650020A0(1610617076LL, 24, 8, 80);
  sub_650020A0(1610617076LL, 23, 1, 0);
  sub_650020A0(1610616836LL, 16, 1, 0);
  sub_650020A0(1610616832LL, 16, 3, 1);
  sub_650020A0(1610616832LL, 8, 8, 240);
  sub_650020A0(1610616832LL, 16, 1, 0);
  sub_650020F4(20LL);
  sub_650020A0(1610616832LL, 17, 1, 1);
  sub_650020F4(20LL);
  if ( (MEMORY[0x60001000] & 0x100000) != 0 )
  {
    n15_1 = 0;
  }
  else
  {
    while ( 1 )
    {
      n15_1 = (MEMORY[0x60001000] >> 12) - 1;
      sub_650020A0(1610616832LL, 12, 4, n15_1);
      sub_650020F4(20LL);
      if ( (MEMORY[0x60001000] & 0x100000) != 0 )
        break;
      if ( !n15_1 )
      {
        sub_650021A0("\r\nDec PCAL fail");
        while ( 1 )
          ;
      }
    }
  }
  n15 = 0;
  sub_650020A0(1610616832LL, 12, 4, 0);
  sub_650020F4(20LL);
  if ( (MEMORY[0x60001000] & 0x100000) != 0 )
  {
    while ( 1 )
    {
      n15 = (MEMORY[0x60001000] >> 12) + 1;
      sub_650020A0(1610616832LL, 12, 4, n15);
      sub_650020F4(10LL);
      if ( (MEMORY[0x60001000] & 0x100000) == 0 )
        break;
      if ( n15 == 15 )
      {
        sub_650021A0("\r\nInc PCAL fail");
        while ( 1 )
          ;
      }
    }
  }
  n13 = sub_65002810(n15_1, n15);
  if ( n13 < 0 )
  {
    sub_650021A0("\r\nzq pcal failed\n");
    while ( 1 )
      ;
  }
  sub_650020A0(1610616832LL, 17, 1, 0);
  sub_650020F4(20LL);
  sub_650020A0(1610616832LL, 18, 1, 1);
  sub_650020F4(10LL);
  if ( (MEMORY[0x60001000] & 0x200000) == 0 )
  {
    while ( 1 )
    {
      n15_1 = ((MEMORY[0x60001000] >> 8) & 0xF) + 1;
      sub_650020A0(1610616832LL, 8, 4, n15_1);
      sub_650020F4(10LL);
      if ( (MEMORY[0x60001000] & 0x200000) != 0 )
        break;
      if ( n15_1 == 15 )
      {
        sub_650021A0("Inc NCAL fail");
        while ( 1 )
          ;
      }
    }
  }
  sub_650020A0(1610616832LL, 8, 4, 15);
  sub_650020F4(20LL);
  if ( (MEMORY[0x60001000] & 0x200000) != 0 )
  {
    while ( 1 )
    {
      n15 = ((MEMORY[0x60001000] >> 8) & 0xF) - 1;
      sub_650020A0(1610616832LL, 8, 4, n15);
      sub_650020F4(10LL);
      if ( (MEMORY[0x60001000] & 0x200000) == 0 )
        break;
      if ( !n15 )
      {
        sub_650021A0("\r\nDec NCAL fail");
        while ( 1 )
          ;
      }
    }
  }
  n8_1 = sub_65002810(n15_1, n15);
  if ( n8_1 < 0 )
  {
    sub_650021A0("\r\nzq ncal failed\n");
    while ( 1 )
      ;
  }
  sub_650020A0(1610616832LL, 18, 1, 0);
  sub_650020A0(1610616832LL, 16, 1, 1);
  if ( n8_1 > 1 )
  {
    sub_650020A0(1610616832LL, 0, 4, n8_1);
    n8 = n8_1;
  }
  else
  {
    sub_650020A0(1610616832LL, 0, 4, 8);
    n8 = 8;
  }
  sub_650020A0(1610620928LL, 0, 4, n8);
  if ( n13 <= 13 )
  {
    sub_650020A0(1610616832LL, 4, 4, n13);
    return sub_650020A0(1610620928LL, 4, 4, n13);
  }
  else
  {
    sub_650020A0(1610616832LL, 4, 4, 11);
    return sub_650020A0(1610620928LL, 4, 4, 11);
  }
}

//----- (00000000650041B4) ----------------------------------------------------
__int64 sub_650041B4()
{
  __int64 n1694583936; // x0

  for ( n1694583936 = 1694583936LL; n1694583936 != 1694584064; n1694583936 += 4LL )
  {
    *(_DWORD *)n1694583936 = 0;
    *(_DWORD *)(n1694583936 - 128) = 0;
    *(_DWORD *)(n1694583936 + 128) = 0;
  }
  return n1694583936;
}

//----- (00000000650041E0) ----------------------------------------------------
__int64 __fastcall sub_650041E0(unsigned int a1, unsigned int n3)
{
  __int16 v2; // w21
  __int16 v3; // w20
  __int64 n1694584064; // x0
  int n0x65010E68_; // [xsp+3Ch] [xbp+3Ch] BYREF

  v2 = a1;
  MEMORY[0x65014C00] = MEMORY[0x65010E78] + MEMORY[0x65010E70];
  MEMORY[0x65014C08] = ((MEMORY[0x65010E58] >> 4) + (MEMORY[0x65010E58] & 0xF)) | (16 * MEMORY[0x65010E6C]);
  MEMORY[0x65014C0C] = MEMORY[0x65010E80];
  MEMORY[0x65014C10] = MEMORY[0x65010E84];
  MEMORY[0x65014C80] = 1515863100;
  MEMORY[0x65014C84] = a1 & 1;
  MEMORY[0x65014C8C] = (a1 >> 1) & 1;
  MEMORY[0x65014C20] = n3;
  n0x65010E68_ = 0;
  sub_65002904(n3, &n0x65010E68_);
  v3 = ((unsigned __int8)(0xFFu >> (n0x65010E68_ + 1) << (n0x65010E68_ + 1)) << 8) | v2;
  if ( MEMORY[0x65010E58] == 64 || MEMORY[0x65010E58] == 65 )
    MEMORY[0x65014C88] = HIBYTE(v3);
  else
    sub_65002260("Invalid type\n");
  n1694584064 = 1694584064LL;
  MEMORY[0x65014D00] = 1515863100;
  if ( (v3 & 0x10) != 0 )
  {
    sub_650020A0(1687226576LL, 0, 1, 0);
    n1694584064 = 1694584068LL;
    MEMORY[0x65014D04] = 1;
  }
  if ( (v3 & 0x20) != 0 )
  {
    n1694584064 = 1694584072LL;
    MEMORY[0x65014D08] = 1;
  }
  return n1694584064;
}

//----- (000000006500436C) ----------------------------------------------------
__int64 sub_6500436C()
{
  __int64 n1683292168; // x0

  if ( MEMORY[0x64550000] == 1581803847 && (MEMORY[0x64550008] & 3) == 3 )
  {
    n1683292168 = 1683292168LL;
    MEMORY[0x64550008] = 427360256;
  }
  else
  {
    MEMORY[0x64550008] = 427360256;
    MEMORY[0x64550004] = 1782132739;
    n1683292168 = 1581803847LL;
    MEMORY[0x64550000] = 1581803847;
  }
  return n1683292168;
}

//----- (00000000650043E8) ----------------------------------------------------
_BYTE *__fastcall sub_650043E8(__int64 n3, unsigned int n5_1)
{
  unsigned int n5; // w19
  int n0x65010E68_; // [xsp+2Ch] [xbp+2Ch] BYREF

  n5 = n3;
  n0x65010E68_ = 0;
  sub_65002904(n3, &n0x65010E68_);
  if ( n5 < n5_1 )
    sub_65003B48(n0x65010E68_);
  sub_65002260("\r\nddr target freq:");
  sub_650021D0(n5);
  return sub_65002260("MHz\n");
}

//----- (0000000065004444) ----------------------------------------------------
_BYTE *sub_65004444()
{
  return sub_65002260("\r\nddr init pass!!!\r\n");
}

//----- (0000000065004450) ----------------------------------------------------
__int64 sub_65004450()
{
  unsigned int n3; // [xsp+3Ch] [xbp+3Ch] BYREF

  n3 = 2133;
  sub_65002868();
  sub_65002260("ddr init start!!!\r\n");
  sub_65003CDC();
  sub_65007678();
  sub_6500436C();
  sub_65003D88();
  sub_65007DA8();
  sub_65003D1C(384LL);
  sub_65007C30();
  sub_65002618();
  sub_65003A24();
  sub_650039A0();
  sub_65007D9C(&n3);
  sub_65002F4C();
  sub_65003BAC(0x8000LL, n3);
  sub_650043E8(1866LL, n3);
  sub_65002FA4();
  sub_650031BC();
  sub_650037F0();
  if ( (unsigned int)sub_6500880C(1, 0LL, 0xFFFuLL) )
  {
    sub_65002260("bist failed\n");
    while ( 1 )
      ;
  }
  sub_65003CFC();
  sub_65007D30();
  sub_650041B4();
  sub_650041E0(0x8000u, n3);
  sub_65004444();
  return sub_65003D6C();
}

//----- (000000006500454C) ----------------------------------------------------
__int64 __fastcall sub_6500454C(int a1, unsigned __int8 a2)
{
  MEMORY[0x65010EBC] = a2;
  MEMORY[0x65010EC0] = a1;
  if ( a1 )
  {
    MEMORY[0x65010EC4] = 12;
    if ( a2 )
    {
      MEMORY[0x65010EC8] = MEMORY[0x65010E64] + MEMORY[0x65010E70] + MEMORY[0x65010E78] - 4096;
      MEMORY[0x65010ECC] = (unsigned __int64)(MEMORY[0x65010E70] + MEMORY[0x65010E78] - 4096LL) >> 32;
      goto LABEL_9;
    }
    MEMORY[0x65010EC8] = MEMORY[0x65010E64];
    goto LABEL_7;
  }
  MEMORY[0x65010EC4] = 3;
  if ( !a2 )
  {
    MEMORY[0x65010EC8] = 0;
LABEL_7:
    MEMORY[0x65010ECC] = 0;
    goto LABEL_9;
  }
  MEMORY[0x65010EC8] = MEMORY[0x65010E70] + MEMORY[0x65010E78] - 4096LL;
LABEL_9:
  sub_650020A0(1610613080LL, 31, 1, a2);
  sub_650020A0(1610613076LL, 3, 1, MEMORY[0x65010EC0]);
  sub_650020A0(1610613076LL, 4, 4, MEMORY[0x65010EC4]);
  MEMORY[0x6000015C] = MEMORY[0x65010EC8];
  return sub_650020A0(1610613080LL, 28, 3, MEMORY[0x65010ECC]);
}

//----- (000000006500466C) ----------------------------------------------------
__int64 sub_6500466C()
{
  sub_650020A0(1610613076LL, 2, 1, 1);
  sub_6500215C(1LL);
  sub_650020A0(1610613076LL, 2, 1, 0);
  return sub_6500215C(1LL);
}

//----- (00000000650046B8) ----------------------------------------------------
__int64 __fastcall sub_650046B8(unsigned int n5)
{
  char v2; // w0

  v2 = 4 * n5;
  while ( ((1 << (v2 + 1)) & MEMORY[0x60000158]) == 0 )
    ;
  if ( ((MEMORY[0x60000158] >> (v2 + 2)) & 1) != 0 && (n5 == 5 || n5 == 3) )
  {
    sub_65002260("train done but failed ");
    sub_65002260("type:");
    sub_650021D0(n5);
    sub_65002260("\n");
    while ( 1 )
      ;
  }
  return 0LL;
}

//----- (0000000065004754) ----------------------------------------------------
__int64 sub_65004754()
{
  __int64 result; // x0

  result = MEMORY[0x65010EB0];
  if ( ((248 >> MEMORY[0x65010EB0]) & 1) != 0 )
  {
    result = 192LL * MEMORY[0x65010EB0];
    if ( MEMORY[0x65010EC0] )
      *(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x600020F4) = 591810662;
    else
      *(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x600010F4) = 591810662;
  }
  return result;
}

//----- (00000000650047B4) ----------------------------------------------------
__int64 __fastcall sub_650047B4(_DWORD *a1)
{
  MEMORY[0x60000158] = *a1 | 0x1111111;
  sub_650020F4(1LL);
  sub_6500466C();
  MEMORY[0x60000158] &= 0xFEEEEEEE;
  sub_650020A0(1610613076LL, 16, 8, 2);
  return sub_650020A0(1610613076LL, 8, 8, 2);
}

//----- (000000006500483C) ----------------------------------------------------
__int64 sub_6500483C()
{
  __int64 i; // x0

  for ( i = 1610612832LL; i != 1610612864; i += 4LL )
    *(_DWORD *)i = *(_DWORD *)((char *)&unk_6500F2F0 + i - 1610612832);
  return sub_650020A0(1610613096LL, 24, 8, 24);
}

//----- (0000000065004888) ----------------------------------------------------
__int64 sub_65004888()
{
  __int64 i; // x0

  for ( i = 1610612832LL; i != 1610612864; i += 4LL )
    *(_DWORD *)i = *(_DWORD *)((char *)&unk_6500F298 + i - 1610612832);
  return sub_650020A0(1610613096LL, 24, 8, 0);
}

//----- (00000000650048D4) ----------------------------------------------------
__int64 sub_650048D4()
{
  int n3; // w3
  unsigned int i; // w19
  __int64 v3; // x0

  sub_650020A0(1610613036LL, 0, 4, 15);
  sub_650020A0(1610613036LL, 11, 1, 1);
  sub_650020A0(1610613036LL, 12, 4, 5);
  if ( MEMORY[0x65010E6C] == 2 )
    n3 = 3;
  else
    n3 = 0;
  sub_650020A0(1610612992LL, 11, 2, n3);
  if ( MEMORY[0x65010E6C] != 2 )
  {
    for ( i = 1610616896; i != 1610618432; i += 192 )
    {
      sub_650020A0(i, 28, 2, 0);
      v3 = i + 4096;
      sub_650020A0(v3, 28, 2, 0);
    }
  }
  sub_6500215C(1LL);
  sub_650020A0(1610613012LL, 24, 1, 0);
  sub_650020A0(1610613016LL, 24, 1, 0);
  sub_650020A0(1610613060LL, 16, 2, 0);
  sub_650020A0(1610613004LL, 12, 1, 1);
  sub_650020A0(1610612996LL, 31, 1, 0);
  return sub_650020A0(1610612932LL, 0, 8, 3);
}

//----- (0000000065004A48) ----------------------------------------------------
__int64 __fastcall sub_65004A48(__int64 a1, int n2)
{
  __int64 result; // x0
  int v4; // w20
  int n192; // w2
  int n128; // w2

  result = sub_650020D0(0x600004E8u, 31, 1);
  v4 = result;
  if ( n2 == 1 )
  {
    sub_650020A0(1610613004LL, 12, 1, 0);
    if ( v4 == 1 )
      n128 = 128;
    else
      n128 = 64;
    sub_650030B0(13, 2, n128);
    sub_650020F4(1LL);
    return sub_650020A0(1610613004LL, 12, 1, 1);
  }
  else if ( n2 == 2 )
  {
    sub_650020A0(1610613004LL, 12, 1, 0);
    if ( v4 )
      n192 = 192;
    else
      n192 = 0;
    sub_650030B0(13, 2, n192);
    sub_650020F4(1LL);
    sub_650020A0(1610613004LL, 12, 1, 1);
    sub_650020A0(1610613080LL, 11, 1, 1);
    sub_650020F4(1LL);
    return sub_650020A0(1610613080LL, 11, 1, 0);
  }
  return result;
}

//----- (0000000065004B70) ----------------------------------------------------
__int64 __fastcall sub_65004B70(char a1)
{
  unsigned int v1; // w1
  __int64 n1610613080; // x0

  v1 = (1 << (4 * a1)) | MEMORY[0x60000158] & 0xFEEEEEEE;
  n1610613080 = 1610613080LL;
  MEMORY[0x60000158] = v1;
  return n1610613080;
}

//----- (0000000065004BA8) ----------------------------------------------------
__int64 __fastcall sub_65004BA8(_BYTE *a1, _BYTE *a2, _BYTE *a3, _BYTE *a4, unsigned __int8 a5)
{
  char v9; // w0
  char n24; // w1
  __int64 result; // x0

  sub_6500454C(MEMORY[0x65010EC0], a5);
  sub_65004B70(5);
  sub_6500466C();
  sub_650020A0(1610613076LL, 1, 1, 1);
  sub_6500215C(1LL);
  sub_650046B8(5u);
  sub_650020A0(1610613076LL, 1, 1, 0);
  if ( MEMORY[0x65010EC0] )
  {
    *a1 = sub_650020D0(0x6000016Cu, 16, 8);
    *a2 = sub_650020D0(0x6000016Cu, 24, 8);
    *a3 = sub_650020D0(0x60000170u, 16, 8);
    n24 = 24;
  }
  else
  {
    *a1 = sub_650020D0(0x6000016Cu, 0, 8);
    *a2 = sub_650020D0(0x6000016Cu, 8, 8);
    v9 = sub_650020D0(0x60000170u, 0, 8);
    n24 = 8;
    *a3 = v9;
  }
  result = sub_650020D0(0x60000170u, n24, 8);
  *a4 = result;
  return result;
}

//----- (0000000065004CFC) ----------------------------------------------------
__int64 __fastcall sub_65004CFC(char a1)
{
  return (*(_DWORD *)(MEMORY[0x65010E48] + 200LL * MEMORY[0x65010EB0] + 4) >> (4 * a1)) & 1;
}

//----- (0000000065004D2C) ----------------------------------------------------
__int64 sub_65004D2C()
{
  __int64 n1610622528; // x0

  MEMORY[0x60001640] |= 0x800u;
  MEMORY[0x60001640] &= ~0x800u;
  MEMORY[0x60002640] |= 0x800u;
  n1610622528 = 1610622528LL;
  MEMORY[0x60002640] &= ~0x800u;
  return n1610622528;
}

//----- (0000000065004DA8) ----------------------------------------------------
int *sub_65004DA8()
{
  int *result; // x0
  unsigned __int64 v1; // x1

  result = &dword_6500F2B8;
  do
  {
    v1 = (unsigned int)(result[2] + 1610616832);
    ++result;
    *(_DWORD *)v1 |= 0x800u;
    *(_DWORD *)(unsigned int)(result[1] + 1610616832) &= ~0x800u;
  }
  while ( result != (int *)&unk_6500F2C8 );
  return result;
}
// 6500F2B8: using guessed type int dword_6500F2B8;

//----- (0000000065004DFC) ----------------------------------------------------
__int64 sub_65004DFC()
{
  __int64 i; // x20
  unsigned int v1; // w21
  unsigned __int64 v2; // x25
  unsigned int n0x7F; // w19
  __int64 result; // x0

  for ( i = 0LL; i != 24; i += 4LL )
  {
    v1 = 192 * MEMORY[0x65010EB0] + 1610616832 + *(_DWORD *)((char *)&unk_6500F310 + i);
    v2 = ((unsigned __int64)*(unsigned int *)v1 >> 16) & 1;
    n0x7F = (unsigned __int8)*(_DWORD *)v1;
    if ( (*(_DWORD *)v1 & 0x20000) != 0 && n0x7F <= 0x7F )
    {
      n0x7F += 128;
      sub_650020A0(v1, 17, 1, 0);
    }
    if ( n0x7F <= 0xBF && (_DWORD)v2 )
    {
      n0x7F += 64;
      sub_650020A0(v1, 16, 1, 0);
    }
    sub_650020A0(v1, 0, 8, n0x7F);
  }
  result = 192LL * MEMORY[0x65010EB0];
  *(_DWORD *)(result + 1610616936) = *(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001068) | 0x10000;
  *(_DWORD *)(result + 1610616956) |= 0x10000u;
  *(_DWORD *)(result + 1610621032) |= 0x10000u;
  *(_DWORD *)(result + 1610621052) |= 0x10000u;
  *(_DWORD *)(result + 1610616952) &= 0xFFFFFF00;
  *(_DWORD *)(result + 1610616972) &= 0xFFFFFF00;
  *(_DWORD *)(result + 1610621048) &= 0xFFFFFF00;
  *(_DWORD *)(result + 1610621068) &= 0xFFFFFF00;
  return result;
}

//----- (0000000065004F90) ----------------------------------------------------
__int64 sub_65004F90()
{
  __int64 n24; // x21
  unsigned int v1; // w19
  unsigned __int64 v2; // x22
  unsigned __int64 v3; // x20
  unsigned int n0x3F; // w3
  __int64 v5; // x0
  char n20; // w1
  int v7; // w2
  char n8; // w2
  int v9; // w3

  sub_650020A0(1610613004LL, 12, 1, 0);
  n24 = 0LL;
  sub_6500215C(2LL);
  do
  {
    v1 = 192 * MEMORY[0x65010EB0] + 1610616832 + *(_DWORD *)((char *)&unk_6500F310 + n24);
    v2 = ((unsigned __int64)*(unsigned int *)v1 >> 16) & 1;
    v3 = ((unsigned __int64)*(unsigned int *)v1 >> 17) & 1;
    n0x3F = (unsigned __int8)*(_DWORD *)v1;
    if ( (*(_DWORD *)v1 & 0x80) != 0 )
    {
      v5 = v1;
      if ( (_DWORD)v3 )
      {
        if ( (_DWORD)v2 )
        {
          sub_650020A0(v1, 0, 8, n0x3F - 128);
          sub_650020A0(v1, 17, v3, 0);
          v5 = v1;
          n20 = 20;
        }
        else
        {
          sub_650020A0(v1, 0, 8, n0x3F - 64);
          v5 = v1;
          n20 = 16;
        }
        v7 = v3;
        goto LABEL_16;
      }
      n20 = 0;
      n8 = 8;
      v9 = n0x3F - 128;
    }
    else
    {
      if ( n0x3F <= 0x3F )
        goto LABEL_17;
      sub_650020A0(v1, 0, 8, n0x3F - 64);
      v5 = v1;
      n20 = 16;
      if ( !(_DWORD)v2 )
        goto LABEL_15;
      n8 = 1;
      if ( (_DWORD)v3 )
      {
        sub_650020A0(v1, 16, 1, 0);
        sub_650020A0(v1, 17, 1, 0);
        v5 = v1;
        n20 = 20;
        goto LABEL_15;
      }
      v9 = 0;
    }
    sub_650020A0(v5, n20, n8, v9);
    v5 = v1;
    n20 = 17;
LABEL_15:
    v7 = 1;
LABEL_16:
    sub_650020A0(v5, n20, v7, v7);
LABEL_17:
    n24 += 4LL;
  }
  while ( n24 != 24 );
  sub_65004D2C();
  sub_65004DA8();
  return sub_650020A0(1610613004LL, 12, 1, 1);
}

//----- (0000000065005140) ----------------------------------------------------
__int64 __fastcall sub_65005140(_DWORD *a1)
{
  __int64 n1610613004; // x0

  MEMORY[0x60001004] = *a1 & 0xFFFFFBFF;
  MEMORY[0x60002004] &= ~0x400u;
  MEMORY[0x6000010C] &= ~0x1000u;
  sub_65004DFC();
  sub_65004D2C();
  sub_65004DA8();
  n1610613004 = 1610613004LL;
  MEMORY[0x6000010C] |= 0x1000u;
  return n1610613004;
}

//----- (00000000650051C4) ----------------------------------------------------
unsigned __int64 sub_650051C4()
{
  char v0; // w1
  unsigned __int64 n0x65010EA8; // x0

  v0 = MEMORY[0x65010EB0];
  if ( (MEMORY[0x65010EA8] == 768 || MEMORY[0x65010EA8] == 533) && (MEMORY[0x60001644] & 0x7Fu) > 0x38 )
    *(_DWORD *)(MEMORY[0x65010E48] + 200LL * MEMORY[0x65010EB0] + 4) &= 0xFFFFFFEE;
  n0x65010EA8 = 0x65010EA8uLL;
  MEMORY[0x65010EB8] = ((248 >> v0) & 1) != 0;
  MEMORY[0x65010ED0] = 0;
  MEMORY[0x65010EDC] = 0;
  MEMORY[0x65010EE0] = 0;
  return n0x65010EA8;
}

//----- (0000000065005258) ----------------------------------------------------
__int64 __fastcall sub_65005258(int a1)
{
  int n200; // w2

  sub_650020A0(1610612992LL, 20, 4, 1 << MEMORY[0x65010EC0]);
  if ( (unsigned int)sub_650020D0(0x600004E8u, 31, 1) )
    n200 = 200;
  else
    n200 = 8;
  sub_650030B0(13, 2, n200);
  sub_650020F4(1LL);
  sub_650030B0(12, 2, a1);
  return sub_650020F4(1LL);
}

//----- (00000000650052E8) ----------------------------------------------------
__int64 __fastcall sub_650052E8(int a1)
{
  __int64 result; // x0
  unsigned int n77; // w20
  unsigned int n114; // w28
  unsigned int n77_4; // w19
  unsigned int n77_3; // w24
  unsigned int n77_2; // w25
  unsigned int n77_1; // w21
  unsigned int v9; // w23
  unsigned int v10; // w22
  int v11; // w27
  unsigned int v12; // w1
  unsigned int n77_5; // w1
  unsigned int v14; // w0
  __int64 v15; // x1
  unsigned int v16; // w2
  __int64 n452; // x0

  result = sub_65004CFC(1);
  if ( (_DWORD)result )
  {
    if ( a1 == 1 )
    {
      n114 = 114;
      n77 = 77;
    }
    else
    {
      n77 = MEMORY[0x65010ED0];
      n114 = MEMORY[0x65010ED0];
    }
    n77_4 = 0;
    sub_650020A0(1610612996LL, 28, 4, 0);
    n77_3 = 0;
    n77_2 = 0;
    n77_1 = 0;
    v9 = 0;
    v10 = 0;
    v11 = 0;
    sub_65004B70(1);
    while ( n77 <= n114 )
    {
      sub_6500466C();
      sub_650020A0(1610613004LL, 12, 1, 0);
      sub_6500215C(1LL);
      sub_650020A0(1610613092LL, 8, 7, n77);
      sub_650020A0(1610613092LL, 0, 8, 16);
      sub_6500215C(1LL);
      sub_650020A0(1610613076LL, 1, 1, 1);
      sub_6500215C(1LL);
      sub_650046B8(1u);
      v12 = MEMORY[0x60000168] & 0x7FF;
      if ( a1 == 1 )
      {
        if ( v12 > v10 )
        {
          n77_1 = n77;
          v10 = MEMORY[0x60000168] & 0x7FF;
        }
        else if ( v12 == v10 )
        {
          n77_2 = n77;
        }
        if ( v12 < v10 && v12 > v9 )
        {
          n77_3 = n77;
          v9 = MEMORY[0x60000168] & 0x7FF;
        }
        else if ( v12 == v9 )
        {
          n77_4 = n77;
        }
      }
      else
      {
        v11 = MEMORY[0x60000168] & 0x7FF;
      }
      n77 += 2;
      sub_650020A0(1610613076LL, 1, 1, 0);
    }
    n77_5 = n77_1;
    v14 = v10;
    if ( a1 == 1 )
    {
      if ( n77_1 >= n77_2 )
        n77_2 = n77_1;
      if ( n77_3 )
      {
        n77_1 = n77_3;
        v10 = v9;
      }
      if ( n77_1 >= n77_4 )
        n77_4 = n77_1;
      MEMORY[0x65010ED0] = ((n77_2 + n77_5) >> 2) + ((n77_4 + n77_1) >> 2);
      if ( !((v10 + v14) >> 1) )
      {
LABEL_27:
        sub_65002260("ca pw is 0\n");
        while ( 1 )
          ;
      }
      sub_65005258(((n77_2 + n77_5) >> 2) + ((n77_4 + n77_1) >> 2));
      v15 = 96LL * MEMORY[0x65010EB0];
      if ( MEMORY[0x65010EC0] )
      {
        v16 = MEMORY[0x65010ED0] | 0xC00 | *(_DWORD *)(MEMORY[0x65010E50] + v15 + 84) & 0xFFFF0000;
        *(_DWORD *)(MEMORY[0x65010E50] + v15 + 84) = v16;
        n452 = 452LL;
      }
      else
      {
        v16 = MEMORY[0x65010ED0] | 0xC00 | *(_DWORD *)(MEMORY[0x65010E50] + v15 + 64) & 0xFFFF0000;
        *(_DWORD *)(MEMORY[0x65010E50] + v15 + 64) = v16;
        n452 = 432LL;
      }
      *(_DWORD *)(v15 + (n452 & 0xFFFFFFFF0000FFFFLL | 0x60000000)) = v16;
      sub_650020F4(1LL);
      sub_6500466C();
    }
    else if ( !v11 )
    {
      goto LABEL_27;
    }
    sub_650020A0(1610612992LL, 20, 4, 3);
    return sub_650020A0(1610613004LL, 12, 1, 1);
  }
  return result;
}

//----- (00000000650055E0) ----------------------------------------------------
__int64 sub_650055E0()
{
  int n0x40_1; // w19
  int n0x40; // w20
  __int64 result; // x0
  __int64 v3; // x0
  unsigned int v4; // w1
  unsigned int *v5; // x2
  unsigned int v6; // w0
  __int64 v7; // x0

  result = sub_65004CFC(0);
  if ( (_DWORD)result )
  {
    if ( MEMORY[0x65010EB0] <= 2uLL )
    {
      v3 = 192LL * MEMORY[0x65010EB0];
      n0x40 = *(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001064);
      if ( (unsigned __int8)n0x40 <= 0x40u )
        v4 = n0x40 & 0xFFFEFF00 | 0x10000;
      else
        v4 = (n0x40 | 0x10000) - 64;
      *(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001064) = v4;
      v5 = (unsigned int *)(v3 + 1610621028);
      n0x40_1 = *(_DWORD *)(v3 + 1610621028);
      if ( (unsigned __int8)n0x40_1 <= 0x40u )
        v6 = n0x40_1 & 0xFFFEFF00 | 0x10000;
      else
        v6 = (n0x40_1 | 0x10000) - 64;
      *v5 = v6;
      sub_65004D2C();
    }
    sub_650020A0(1610612996LL, 28, 4, 0);
    sub_65004B70(0);
    sub_6500466C();
    sub_650020A0(1610613004LL, 12, 1, 0);
    sub_650020A0(1610613092LL, 8, 7, MEMORY[0x65010ED0]);
    sub_6500215C(1LL);
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(0);
    sub_650020A0(1610613076LL, 1, 1, 0);
    if ( MEMORY[0x65010EB0] <= 2uLL )
    {
      v7 = 192LL * MEMORY[0x65010EB0];
      *(_DWORD *)(v7 + 1610616932) = n0x40;
      *(_DWORD *)(v7 + 1610621028) = n0x40_1;
      sub_65004D2C();
    }
    return sub_650020A0(1610613004LL, 12, 1, 1);
  }
  return result;
}
// 65005740: variable 'v1' is possibly undefined
// 6500574C: variable 'v0' is possibly undefined

//----- (0000000065005788) ----------------------------------------------------
__int64 sub_65005788()
{
  __int64 result; // x0

  result = sub_65004CFC(3);
  if ( (_DWORD)result )
  {
    sub_65004B70(3);
    sub_6500466C();
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(3u);
    return sub_650020A0(1610613076LL, 1, 1, 0);
  }
  return result;
}

//----- (00000000650057F4) ----------------------------------------------------
__int64 __fastcall sub_650057F4(int n2)
{
  __int64 result; // x0
  int n64; // w1
  int n16; // w24
  int n127; // w1
  unsigned int v6; // w20
  unsigned int n16_1; // w21
  unsigned int n16_3; // w27
  unsigned int n16_4; // w26
  unsigned int v10; // w28
  int v11; // w1
  unsigned __int16 n8436; // w0
  char n16_2; // w19
  unsigned int v14; // w19
  int v15; // w5
  int v16; // w0
  unsigned int n3; // w1
  unsigned int v18; // w20
  int v19; // w1
  __int64 v20; // x0
  unsigned int v21; // w3
  int v22; // w2
  unsigned __int16 n8436_1; // w2
  int v24; // [xsp+70h] [xbp+70h]
  unsigned int n64_1; // [xsp+8Ch] [xbp+8Ch]

  result = sub_65004CFC(4);
  if ( (_DWORD)result )
  {
    sub_65004B70(4);
    sub_6500215C(1LL);
    if ( n2 == 1 )
    {
      if ( MEMORY[0x65010EB8] )
      {
        if ( MEMORY[0x65010E58] == 65 )
          n64 = 64;
        else
          n64 = 24;
        n64_1 = n64;
        if ( MEMORY[0x65010E58] == 65 )
          n16 = 16;
        else
          n16 = 4;
      }
      else
      {
        if ( MEMORY[0x65010E58] == 65 )
          n127 = 127;
        else
          n127 = 60;
        n64_1 = n127;
        if ( MEMORY[0x65010E58] == 65 )
          n16 = 32;
        else
          n16 = 16;
      }
    }
    else
    {
      n16 = MEMORY[0x65010EDC];
      n64_1 = MEMORY[0x65010EDC];
    }
    v6 = 0;
    n16_1 = n16;
    n16_3 = 0;
    n16_4 = 0;
    v10 = 0;
    while ( n16_1 <= n64_1 )
    {
      sub_6500466C();
      sub_650020A0(1610613004LL, 12, 1, 1);
      sub_6500215C(2LL);
      if ( MEMORY[0x65010EC0] )
      {
        sub_650020A0((unsigned int)(192 * MEMORY[0x65010EB0] + 1610621172), 23, 1, 0);
        v11 = MEMORY[0x65010EB0];
        n8436 = 8436;
      }
      else
      {
        sub_650020A0((unsigned int)(192 * MEMORY[0x65010EB0] + 1610617076), 23, 1, 0);
        v11 = MEMORY[0x65010EB0];
        n8436 = 4340;
      }
      sub_650020A0((n8436 | 0x60000000u) + 192 * v11, 24, 8, n16_1);
      if ( n2 == 2 )
        MEMORY[0x600000C0] = -1431655766;
      sub_650020A0(1610613076LL, 1, 1, 1);
      sub_6500215C(1LL);
      sub_650046B8(4u);
      if ( MEMORY[0x65010EC0] )
        n16_2 = 16;
      else
        n16_2 = 0;
      v14 = (unsigned __int8)(MEMORY[0x60000170] >> n16_2);
      v15 = (unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000106C) | (*(_DWORD *)(192LL
                                                                                               * MEMORY[0x65010EB0]
                                                                                               + 0x60002080) << 24) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001080) << 8) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000206C) << 16);
      if ( n2 == 2 )
      {
        v24 = (unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000106C) | (*(_DWORD *)(192LL
                                                                                                 * MEMORY[0x65010EB0]
                                                                                                 + 0x60002080) << 24) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001080) << 8) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000206C) << 16);
        sub_650020A0(1610613076LL, 1, 1, 0);
        sub_6500466C();
        MEMORY[0x600000C0] = 1431655765;
        sub_650020A0(1610613076LL, 1, 1, 1);
        sub_6500215C(1LL);
        sub_650046B8(4u);
        v15 = v24;
        MEMORY[0x600000C0] = 0;
      }
      v16 = (unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001070) | (*(_DWORD *)(192LL
                                                                                               * MEMORY[0x65010EB0]
                                                                                               + 0x60002084) << 24) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001084) << 8) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60002070) << 16);
      if ( n2 == 1 )
      {
        if ( n16_1 != n16 )
        {
          if ( v6 <= v14 )
          {
            n3 = v14 - v6;
            v18 = v6 + 2;
          }
          else
          {
            n3 = v6 - v14;
            v18 = v6 - 2;
          }
          if ( n3 >= 3 )
            v14 = v18;
        }
        if ( v14 <= v10 )
        {
          if ( v14 == v10 )
            n16_3 = n16_1;
        }
        else
        {
          n16_4 = n16_1;
          MEMORY[0x65010E9C] = v15;
          MEMORY[0x65010E98] = v16;
          v10 = v14;
        }
      }
      else
      {
        v10 = v14;
        MEMORY[0x65010E9C] = v15;
        MEMORY[0x65010E98] = v16;
        v14 = v6;
      }
      n16_1 += 2;
      sub_650020A0(1610613076LL, 1, 1, 0);
      v6 = v14;
    }
    if ( !v10 )
    {
      sub_65002260("rd pw is 0\n");
      while ( 1 )
        ;
    }
    sub_6500215C(2LL);
    if ( n2 == 1 )
    {
      v19 = MEMORY[0x65010EB0];
      if ( n16_3 < n16_4 )
        n16_3 = n16_4;
      v20 = MEMORY[0x65010E48] + 200LL * MEMORY[0x65010EB0];
      v21 = (n16_3 + n16_4) >> 1;
      v22 = *(_DWORD *)(v20 + 188);
      MEMORY[0x65010EDC] = v21;
      *(_DWORD *)(v20 + 188) = v22 & 0xFFFFFF | (v21 << 24);
      if ( MEMORY[0x65010EC0] )
        n8436_1 = 8436;
      else
        n8436_1 = 4340;
      sub_650020A0((n8436_1 | 0x60000000u) + 192 * v19, 24, 8, v21);
      sub_6500466C();
    }
    return sub_6500466C();
  }
  return result;
}

//----- (0000000065005C68) ----------------------------------------------------
__int64 sub_65005C68()
{
  __int64 result; // x0
  unsigned int v1; // w28
  int n2; // w20
  char *v3; // x6
  _DWORD *n0x65010E9C; // x5
  _DWORD *n0x65010E98; // x4
  char *v6; // x19
  unsigned int v7; // w1
  int v8; // w25
  unsigned int v9; // w26
  unsigned int v10; // w21
  unsigned int v11; // w22
  int v12; // w1
  int v13; // w3
  int v14; // w3
  unsigned int v15; // w1
  int v16; // w3
  _DWORD *n0x65010E98_1; // [xsp+60h] [xbp+60h]
  _DWORD *n0x65010E9C_1; // [xsp+68h] [xbp+68h]
  char *v19; // [xsp+70h] [xbp+70h]
  unsigned int v20; // [xsp+8Ch] [xbp+8Ch]

  result = sub_65004CFC(4);
  if ( (_DWORD)result )
  {
    sub_65004B70(4);
    sub_6500215C(1LL);
    sub_6500466C();
    sub_650020A0(1610613004LL, 12, 1, 1);
    sub_6500215C(2LL);
    MEMORY[0x600000C0] = -1431655766;
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(4u);
    v1 = (unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000106C) | (*(_DWORD *)(192LL * MEMORY[0x65010EB0]
                                                                                            + 0x60002080) << 24) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001080) << 8) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x6000206C) << 16);
    sub_650020A0(1610613076LL, 1, 1, 0);
    sub_6500466C();
    MEMORY[0x600000C0] = 1431655765;
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(4u);
    MEMORY[0x600000C0] = 0;
    n2 = 0;
    v20 = (unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001070) | (*(_DWORD *)(192LL
                                                                                             * MEMORY[0x65010EB0]
                                                                                             + 0x60002084) << 24) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60001084) << 8) | ((unsigned __int8)*(_DWORD *)(192LL * MEMORY[0x65010EB0] + 0x60002070) << 16);
    sub_650020A0(1610613076LL, 1, 1, 0);
    v3 = (char *)&unk_65010000;
    n0x65010E9C = (_DWORD *)0x65010E9C;
    n0x65010E98 = (_DWORD *)0x65010E98;
    do
    {
      v6 = v3 + 3752;
      v7 = n2 + 2 * *((_DWORD *)v3 + 944);
      v8 = *n0x65010E98 >> (8 * v7);
      v9 = (unsigned __int8)(v20 >> (8 * v7));
      v10 = (unsigned __int8)(*n0x65010E9C >> (8 * v7));
      v11 = (unsigned __int8)(v1 >> (8 * v7));
      v12 = dword_6500F338[v7];
      v13 = v11 - v10;
      if ( v11 <= v10 )
        v13 = v10 - v11;
      n0x65010E98_1 = n0x65010E98;
      n0x65010E9C_1 = n0x65010E9C;
      v19 = v3;
      sub_650020A0(192 * (unsigned int)*((_QWORD *)v3 + 470) + 1610616832 + v12, 17, 7, v13);
      sub_650020A0(
        192 * (unsigned int)*((_QWORD *)v6 + 1) + 1610616832 + dword_6500F338[n2 + 2 * *((_DWORD *)v6 + 6)],
        16,
        1,
        v11 > v10);
      sub_650020A0(
        192 * (unsigned int)*((_QWORD *)v6 + 1) + 1610616832 + dword_6500F338[n2 + 2 * *((_DWORD *)v6 + 6)],
        30,
        1,
        1);
      if ( v11 < v10 )
        v14 = v10;
      else
        v14 = v11;
      sub_650020A0(
        192 * (unsigned int)*((_QWORD *)v6 + 1) + 1610616832 + dword_6500F2E0[n2 + 2 * *((_DWORD *)v6 + 6)],
        0,
        8,
        v14);
      v15 = n2 + 2 * *((_DWORD *)v6 + 6);
      ++n2;
      if ( v9 < (unsigned __int8)v8 )
        v16 = (unsigned __int8)v8;
      else
        v16 = v9;
      sub_650020A0(192 * (unsigned int)*((_QWORD *)v6 + 1) + 1610616832 + dword_6500F2D0[v15], 0, 8, v16);
      n0x65010E98 = n0x65010E98_1;
      n0x65010E9C = n0x65010E9C_1;
      v3 = v19;
    }
    while ( n2 != 2 );
    sub_65004DA8();
    return sub_650020F4(1LL);
  }
  return result;
}
// 6500F2D0: using guessed type _DWORD dword_6500F2D0[4];
// 6500F2E0: using guessed type _DWORD dword_6500F2E0[4];
// 6500F338: using guessed type _DWORD dword_6500F338[4];

//----- (000000006500600C) ----------------------------------------------------
__int64 __fastcall sub_6500600C(int a1)
{
  unsigned int v3; // [xsp+3Ch] [xbp+3Ch] BYREF

  v3 = 0;
  sub_650020A0(1610613004LL, 12, 1, 0);
  sub_6500215C(2LL);
  sub_650020A0(1610612992LL, 20, 4, 1 << MEMORY[0x65010EC0]);
  sub_650030B0(14, 2, a1);
  sub_6500215C(100LL);
  sub_65003340(14, 2, &v3);
  if ( a1 != (unsigned __int8)(v3 >> (8 * MEMORY[0x65010EC0])) )
  {
    while ( 1 )
      ;
  }
  sub_650020A0(1610612996LL, 31, 1, 0);
  sub_6500215C(5LL);
  sub_650020A0(1610612992LL, 20, 4, 3);
  sub_6500215C(2LL);
  return sub_650020A0(1610613004LL, 12, 1, 1);
}

//----- (000000006500611C) ----------------------------------------------------
__int64 sub_6500611C()
{
  __int64 result; // x0
  __int64 v1; // x7
  __int64 v2; // x1
  __int64 v3; // x2
  unsigned __int64 v4; // x4
  __int64 v5; // x0
  __int64 v6; // x3
  unsigned int v7; // w0
  int v8; // w0
  __int64 v9; // x3
  int v10; // w0
  __int64 v11; // x4
  __int64 v12; // x0
  int v13; // w2
  unsigned int v14; // w9
  unsigned __int64 v15; // x4
  __int64 v16; // x6
  __int64 v17; // x3
  int v18; // w0
  unsigned int v19; // w0
  __int64 v20; // x0
  int v21; // w0
  __int64 v22; // x4
  __int64 v23; // x0
  int v24; // w2
  unsigned __int8 n0x80; // [xsp+48h] [xbp+48h] BYREF
  unsigned __int8 n0x80_2; // [xsp+49h] [xbp+49h] BYREF
  char v27; // [xsp+4Ah] [xbp+4Ah] BYREF
  char v28; // [xsp+4Bh] [xbp+4Bh] BYREF
  unsigned __int8 n0x80_1; // [xsp+4Ch] [xbp+4Ch] BYREF
  unsigned __int8 n0x80_3; // [xsp+4Dh] [xbp+4Dh] BYREF
  char v31; // [xsp+4Eh] [xbp+4Eh] BYREF
  char v32; // [xsp+4Fh] [xbp+4Fh] BYREF

  n0x80 = 0;
  n0x80_2 = 0;
  v27 = 0;
  v28 = 0;
  n0x80_1 = 0;
  n0x80_3 = 0;
  v31 = 0;
  v32 = 0;
  result = sub_65004CFC(5);
  if ( (_DWORD)result )
  {
    sub_650020A0(
      dword_6500F328[2 * (MEMORY[0x65010EC0] & 0x7FFFFFFF)] + 192 * MEMORY[0x65010EB0] + 1610616832,
      16,
      2,
      3);
    sub_650020A0(192 * MEMORY[0x65010EB0] + 1610616832 + dword_6500F328[2 * MEMORY[0x65010EC0] + 1], 16, 2, 3);
    sub_650020A0(1610613080LL, 7, 1, 1);
    sub_65004BA8(&n0x80, &n0x80_2, &v27, &v28, 0);
    if ( MEMORY[0x65010E6C] == 2 )
      sub_65004BA8(&n0x80_1, &n0x80_3, &v31, &v32, 1u);
    v1 = MEMORY[0x65010EB0];
    v2 = 192LL * MEMORY[0x65010EB0] + 1610616832;
    v3 = dword_6500F328[2 * (MEMORY[0x65010EC0] & 0x7FFFFFFF)];
    v4 = *(unsigned int *)(v3 + v2);
    v5 = (v4 >> 16) & 1;
    v6 = (v4 >> 17) & 1;
    if ( !n0x80 && (MEMORY[0x65010E6C] != 2 || !n0x80_1) )
    {
      if ( (_DWORD)v5 )
      {
        v7 = *(_DWORD *)(v3 + v2) & 0xFFFEFFFF;
LABEL_27:
        *(_DWORD *)(v3 + v2) = v7;
        goto LABEL_28;
      }
      v8 = *(_DWORD *)(v3 + v2);
      if ( (_DWORD)v6 )
      {
        *(_DWORD *)(v3 + v2) = v8 | 0x10000;
        v9 = dword_6500F328[2 * (MEMORY[0x65010EC0] & 0x7FFFFFFF)];
        *(_DWORD *)(v9 + v2) &= ~0x20000u;
LABEL_28:
        v12 = dword_6500F328[2 * (MEMORY[0x65010EC0] & 0x7FFFFFFF)];
        v13 = *(_DWORD *)(v12 + v2) | 0x40000;
        goto LABEL_29;
      }
      v10 = v8 | 0x30000;
      goto LABEL_22;
    }
    v11 = (v4 >> 18) & 1;
    if ( (unsigned __int8)(n0x80 - 1) <= 0xBFu || (unsigned __int8)(n0x80_1 - 1) <= 0xBFu )
    {
      if ( n0x80 > 0x80u && n0x80_1 > 0x80u )
      {
        if ( !(_DWORD)v5 )
        {
          v10 = *(_DWORD *)(v3 + v2) | 0x10000;
          goto LABEL_22;
        }
        if ( !(_DWORD)v6 )
        {
          *(_DWORD *)(v3 + v2) &= ~0x10000u;
          v12 = dword_6500F328[2 * (MEMORY[0x65010EC0] & 0x7FFFFFFF)];
          v13 = *(_DWORD *)(v12 + v2) | 0x20000;
LABEL_29:
          *(_DWORD *)(v12 + v2) = v13;
          goto LABEL_30;
        }
        if ( !(_DWORD)v11 )
        {
          v7 = *(_DWORD *)(v3 + v2) & 0xFFFCFFFF;
          goto LABEL_27;
        }
      }
    }
    else
    {
      if ( !(_DWORD)v6 )
      {
        v10 = *(_DWORD *)(v3 + v2) | 0x20000;
LABEL_22:
        *(_DWORD *)(v3 + v2) = v10;
        goto LABEL_30;
      }
      if ( !(_DWORD)v11 )
      {
        v7 = *(_DWORD *)(v3 + v2) & 0xFFFDFFFF;
        goto LABEL_27;
      }
    }
LABEL_30:
    v14 = dword_6500F328[2 * MEMORY[0x65010EC0] + 1];
    v15 = *(unsigned int *)(v14 + 192 * v1 + 0x60001000);
    v16 = (v15 >> 16) & 1;
    v17 = (v15 >> 17) & 1;
    if ( !n0x80_2 && (MEMORY[0x65010E6C] != 2 || !n0x80_1) )
    {
      v18 = *(_DWORD *)(v14 + v2);
      if ( (_DWORD)v16 )
      {
        v19 = v18 & 0xFFFEFFFF;
LABEL_53:
        *(_DWORD *)(v14 + v2) = v19;
        goto LABEL_54;
      }
      if ( (_DWORD)v17 )
      {
        *(_DWORD *)(v14 + v2) = v18 | 0x10000;
        v20 = dword_6500F328[2 * MEMORY[0x65010EC0] + 1];
        *(_DWORD *)(v20 + v2) &= ~0x20000u;
LABEL_54:
        v23 = dword_6500F328[2 * MEMORY[0x65010EC0] + 1];
        v24 = *(_DWORD *)(v23 + v2) | 0x40000;
        goto LABEL_55;
      }
      v21 = v18 | 0x30000;
      goto LABEL_48;
    }
    v22 = (v15 >> 18) & 1;
    if ( (unsigned __int8)(n0x80_2 - 1) <= 0xBFu || (unsigned __int8)(n0x80_3 - 1) <= 0xBFu )
    {
      if ( n0x80_2 <= 0x80u || n0x80_3 <= 0x80u )
        goto LABEL_56;
      if ( !(_DWORD)v16 )
      {
        v21 = *(_DWORD *)(v14 + v2) | 0x10000;
LABEL_48:
        *(_DWORD *)(v14 + v2) = v21;
        goto LABEL_56;
      }
      if ( !(_DWORD)v17 )
      {
        *(_DWORD *)(v14 + v2) &= ~0x10000u;
        v23 = dword_6500F328[2 * MEMORY[0x65010EC0] + 1];
        v24 = *(_DWORD *)(v23 + v2) | 0x20000;
LABEL_55:
        *(_DWORD *)(v23 + v2) = v24;
        goto LABEL_56;
      }
      if ( !(_DWORD)v22 )
      {
        v19 = *(_DWORD *)(v14 + v2) & 0xFFFCFFFF;
        goto LABEL_53;
      }
    }
    else if ( (_DWORD)v17 )
    {
      if ( !(_DWORD)v22 )
      {
        v19 = *(_DWORD *)(v14 + v2) & 0xFFFDFFFF;
        goto LABEL_53;
      }
    }
    else
    {
      sub_650020A0(192 * (_DWORD)v1 + 1610616832 + v14, 17, 1, 1);
    }
LABEL_56:
    sub_6500466C();
    return sub_650020A0(1610613080LL, 7, 1, 0);
  }
  return result;
}
// 6500F328: using guessed type unsigned int dword_6500F328[4];

//----- (000000006500655C) ----------------------------------------------------
__int64 sub_6500655C()
{
  __int64 result; // x0

  result = sub_65004CFC(5);
  if ( (_DWORD)result )
  {
    sub_65004B70(5);
    sub_6500466C();
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(5u);
    sub_650020A0(1610613076LL, 1, 1, 0);
    return sub_6500466C();
  }
  return result;
}

//----- (00000000650065CC) ----------------------------------------------------
__int64 __fastcall sub_650065CC(int a1)
{
  __int64 result; // x0
  unsigned int n45; // w24
  unsigned int n10; // w19
  unsigned int n10_2; // w20
  unsigned int n10_1; // w22
  unsigned int v7; // w23
  unsigned int v8; // w0
  __int64 v9; // x1
  int v10; // w4
  __int64 v11; // x3
  __int64 n452; // x0
  int v13; // w2

  result = sub_65004CFC(6);
  if ( !(_DWORD)result )
    return result;
  sub_65004B70(6);
  if ( a1 == 1 )
  {
    if ( MEMORY[0x65010EB8] )
    {
      if ( MEMORY[0x65010E58] == 65 )
        n45 = 45;
      else
        n45 = 30;
      if ( MEMORY[0x65010E58] == 65 )
        n10 = 10;
      else
        n10 = 5;
    }
    else
    {
      if ( MEMORY[0x65010E58] == 65 )
        n45 = 112;
      else
        n45 = 106;
      if ( MEMORY[0x65010E58] == 65 )
        n10 = 77;
      else
        n10 = 64;
    }
  }
  else
  {
    n45 = MEMORY[0x65010EE0];
    n10 = MEMORY[0x65010EE0];
  }
  n10_2 = 0;
  n10_1 = 0;
  v7 = 0;
  while ( n10 <= n45 )
  {
    sub_6500466C();
    sub_6500600C(n10);
    sub_650020A0(1610613092LL, 16, 8, 16);
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(6u);
    v8 = (unsigned __int8)(MEMORY[0x60000170] >> (16 * MEMORY[0x65010EC0]));
    if ( a1 != 1 )
    {
      MEMORY[0x65010EA0] = ((unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0] + 1]
                                                       + 192LL * MEMORY[0x65010EB0]
                                                       + 0x60001000) << 8) | (unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0]] + 192LL * MEMORY[0x65010EB0] + 0x60001000);
      goto LABEL_25;
    }
    if ( v8 > v7 )
    {
      MEMORY[0x65010EA0] = ((unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0] + 1]
                                                       + 192LL * MEMORY[0x65010EB0]
                                                       + 0x60001000) << 8) | (unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0]] + 192LL * MEMORY[0x65010EB0] + 0x60001000);
      n10_1 = n10;
LABEL_25:
      v7 = (unsigned __int8)(MEMORY[0x60000170] >> (16 * MEMORY[0x65010EC0]));
      goto LABEL_26;
    }
    if ( v8 == v7 )
      n10_2 = n10;
LABEL_26:
    n10 += 2;
    sub_650020A0(1610613076LL, 1, 1, 0);
  }
  sub_6500466C();
  if ( !v7 )
  {
    sub_65002260("wr pw is 0\n");
    while ( 1 )
      ;
  }
  sub_6500215C(2LL);
  if ( a1 == 1 )
  {
    if ( n10_1 >= n10_2 )
      n10_2 = n10_1;
    MEMORY[0x65010EE0] = (n10_2 + n10_1) >> 1;
    sub_6500600C(MEMORY[0x65010EE0]);
    v9 = 96LL * MEMORY[0x65010EB0];
    v10 = MEMORY[0x65010EE0] | 0xE00;
    v11 = MEMORY[0x65010E50] + 96LL * MEMORY[0x65010EB0];
    if ( MEMORY[0x65010EC0] )
    {
      MEMORY[0x65010EE8] = MEMORY[0x65010EE0];
      n452 = 452LL;
      v13 = *(unsigned __int16 *)(v11 + 84) | (v10 << 16);
      *(_DWORD *)(v11 + 84) = v13;
    }
    else
    {
      MEMORY[0x65010EE4] = MEMORY[0x65010EE0];
      n452 = 432LL;
      v13 = *(unsigned __int16 *)(v11 + 64) | (v10 << 16);
      *(_DWORD *)(v11 + 64) = v13;
    }
    *(_DWORD *)(v9 + (n452 & 0xFFFFFFFF0000FFFFLL | 0x60000000)) = v13;
    sub_6500466C();
  }
  sub_650020A0(1610613004LL, 12, 1, 1);
  return sub_6500466C();
}
// 6500F328: using guessed type unsigned int dword_6500F328[4];

//----- (0000000065006890) ----------------------------------------------------
int *sub_65006890()
{
  int *result; // x0
  int n2; // w19
  unsigned int v2; // w27
  unsigned int v3; // w21
  unsigned int v4; // w22
  int v5; // w3
  int v6; // w3
  int v7; // w1

  result = (int *)sub_65004CFC(6);
  if ( (_DWORD)result )
  {
    sub_65004B70(6);
    sub_6500466C();
    sub_650020A0(1610613076LL, 1, 1, 1);
    sub_6500215C(1LL);
    sub_650046B8(6u);
    n2 = 0;
    v2 = ((unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0] + 1]
                                     + 192LL * MEMORY[0x65010EB0]
                                     + 0x60001000) << 8) | (unsigned __int8)*(_DWORD *)(dword_6500F328[2 * MEMORY[0x65010EC0]]
                                                                                      + 192LL * MEMORY[0x65010EB0]
                                                                                      + 0x60001000);
    sub_650020A0(1610613076LL, 1, 1, 0);
    sub_6500215C(1LL);
    do
    {
      v3 = (unsigned __int8)(MEMORY[0x65010EA0] >> (8 * n2));
      v4 = (unsigned __int8)(v2 >> (8 * n2));
      v5 = v4 - v3;
      if ( v4 <= v3 )
        v5 = v3 - v4;
      sub_650020A0(
        (unsigned int)(192 * MEMORY[0x65010EB0] + 1610616832 + dword_6500F338[n2 + 2 * MEMORY[0x65010EC0]]),
        1,
        7,
        v5);
      sub_650020A0(
        (unsigned int)(192 * MEMORY[0x65010EB0] + 1610616832 + dword_6500F338[n2 + 2 * MEMORY[0x65010EC0]]),
        0,
        1,
        v4 > v3);
      sub_650020A0(
        (unsigned int)(192 * MEMORY[0x65010EB0] + 1610616832 + dword_6500F338[n2 + 2 * MEMORY[0x65010EC0]]),
        31,
        1,
        1);
      if ( v4 > v3 )
        v6 = v3;
      else
        v6 = (unsigned __int8)(v2 >> (8 * n2));
      v7 = n2 + 2 * MEMORY[0x65010EC0];
      ++n2;
      sub_650020A0(192 * MEMORY[0x65010EB0] + 1610616832 + dword_6500F328[v7], 0, 8, v6);
    }
    while ( n2 != 2 );
    return sub_65004DA8();
  }
  return result;
}
// 6500F328: using guessed type unsigned int dword_6500F328[4];
// 6500F338: using guessed type _DWORD dword_6500F338[4];

//----- (0000000065006AB8) ----------------------------------------------------
__int64 sub_65006AB8()
{
  int v0; // w23
  int v1; // w22
  int v2; // w20
  int n192; // w2
  int n192_1; // w2

  sub_650020A0(1610613080LL, 7, 1, 1);
  sub_650020A0(1610612992LL, 20, 4, 1 << MEMORY[0x65010EC0]);
  v0 = sub_650020D0(96 * MEMORY[0x65010EB0] + 1610613164, 0, 8);
  v1 = sub_650020D0(96 * MEMORY[0x65010EB0] + 1610613176, 0, 8);
  v2 = sub_650020D0(0x600004E8u, 31, 1);
  sub_650020A0(1610613004LL, 12, 1, 0);
  if ( v2 == 1 )
    n192 = 0;
  else
    n192 = 192;
  sub_650030B0(13, 2, n192);
  sub_650020F4(1LL);
  sub_650030B0(11, 2, v0);
  sub_650020F4(1LL);
  sub_650030B0(22, 2, v1);
  sub_650020F4(1LL);
  if ( v2 == 1 )
    n192_1 = 192;
  else
    n192_1 = 0;
  sub_650030B0(13, 2, n192_1);
  sub_650020F4(1LL);
  return sub_650020A0(1610613004LL, 12, 1, 1);
}

//----- (0000000065006C14) ----------------------------------------------------
__int64 __fastcall sub_65006C14(unsigned int n4, int a2)
{
  int v2; // w22
  int v3; // w21
  int n29; // w19
  __int64 v5; // x0
  __int64 v6; // x1
  __int64 v7; // x1

  v2 = (240 >> n4) & 1;
  v3 = (248 >> n4) & 1;
  n29 = 29;
  if ( n4 > 4 )
  {
    if ( a2 )
      n29 = MEMORY[0x65010EE8];
    else
      n29 = MEMORY[0x65010EE4];
  }
  sub_650020A0(1610612992LL, 20, 4, 1 << a2);
  sub_650020A0(1610613004LL, 12, 1, 0);
  if ( v2 )
  {
    sub_650030B0(11, 2, 84);
    sub_650030B0(14, 2, n29);
    sub_650030B0(22, 2, 53);
    v5 = 96LL * MEMORY[0x65010EB0];
    *(_DWORD *)(v5 + 1610613164) = 190057300;
    *(_DWORD *)(v5 + 1610613176) = 218633781;
    v6 = MEMORY[0x65010E50] + v5;
    *(_DWORD *)(v6 + 60) = 190057300;
    *(_DWORD *)(v6 + 72) = 218633781;
  }
  else if ( v3 )
  {
    sub_650030B0(11, 2, 4);
    sub_650030B0(14, 2, n29);
    v7 = 96LL * MEMORY[0x65010EB0];
    *(_DWORD *)(v7 + 1610613164) = 184814340;
    *(_DWORD *)(MEMORY[0x65010E50] + v7 + 60) = 184814340;
  }
  return sub_650020A0(1610613004LL, 12, 1, 1);
}

//----- (0000000065006DB8) ----------------------------------------------------
__int64 __fastcall sub_65006DB8(__int64 n2)
{
  if ( (_DWORD)n2 == 1 )
    return sub_650020A0(1610613080LL, 7, 1, 0);
  if ( (_DWORD)n2 == 2 )
    return sub_65004754();
  return n2;
}

//----- (0000000065006DE8) ----------------------------------------------------
__int64 sub_65006DE8()
{
  int n2; // w19
  int v1; // w0

  n2 = 0;
  do
  {
    sub_650020A0(1610612992LL, 20, 4, 1 << n2);
    v1 = n2++;
    sub_6500454C(v1, 0);
    sub_65006AB8();
    MEMORY[0x60000060] = 1023;
    sub_650052E8(1);
    sub_65006DB8(1LL);
    sub_650055E0();
    sub_650052E8(2);
  }
  while ( n2 != 2 );
  MEMORY[0x65010EC0] = 0;
  sub_65006C14(MEMORY[0x65010EB0], 0);
  sub_65004754();
  MEMORY[0x65010EC0] = 1;
  sub_65006C14(MEMORY[0x65010EB0], 1);
  return sub_65004754();
}

//----- (0000000065006EA0) ----------------------------------------------------
__int64 sub_65006EA0()
{
  int i; // w19
  __int64 result; // x0

  for ( i = 0; i != 2; ++i )
  {
    sub_6500454C(i, 0);
    sub_6500483C();
    sub_650057F4(1);
    sub_65005788();
    sub_650057F4(2);
    result = MEMORY[0x65010E6C];
    if ( MEMORY[0x65010E6C] == 2 )
    {
      sub_6500454C(i, 1u);
      sub_65005788();
      result = sub_65005C68();
    }
  }
  return result;
}

//----- (0000000065006F14) ----------------------------------------------------
int *sub_65006F14()
{
  int i; // w19
  int *result; // x0

  for ( i = 0; i != 2; ++i )
  {
    sub_6500454C(i, 0);
    sub_65004888();
    sub_6500611C();
    sub_6500454C(i, 0);
    sub_650065CC(1);
    sub_6500655C();
    sub_650065CC(2);
    result = (int *)MEMORY[0x65010E6C];
    if ( MEMORY[0x65010E6C] == 2 )
    {
      sub_6500454C(i, 1u);
      sub_6500655C();
      result = sub_65006890();
    }
  }
  return result;
}

//----- (0000000065006F98) ----------------------------------------------------
int *sub_65006F98()
{
  sub_65006DE8();
  sub_65006EA0();
  return sub_65006F14();
}

//----- (0000000065006FB0) ----------------------------------------------------
__int64 __fastcall sub_65006FB0(unsigned int n0x215, unsigned int a2)
{
  __int64 v4; // x0
  __int64 v5; // x0
  int v6; // w19
  int n16; // w20
  int n128; // w2
  int n16_1; // w19
  int v10; // w24
  unsigned __int64 v11; // x25
  unsigned __int64 v12; // x28
  int v13; // w21
  unsigned int n0xF; // w20
  int v15; // w20

  MEMORY[0x65010EA8] = n0x215;
  MEMORY[0x65010EB0] = a2;
  sub_65004A48(a2, 1);
  sub_65003B48(MEMORY[0x65010EB0]);
  sub_65004A48(MEMORY[0x65010EB0], 2);
  v4 = sub_650020A0(1610613076LL, 0, 1, 1);
  v5 = sub_650047AC(v4);
  if ( n0x215 > 0x215 )
    sub_65005138(v5);
  sub_650051C4();
  sub_65006F98();
  if ( n0x215 > 0x215 )
    sub_65004F90();
  if ( a2 - 3 <= 3 )
  {
    v6 = 192 * (a2 + 1);
    *(_DWORD *)(unsigned int)(v6 + 1610616932) = *(_DWORD *)(unsigned int)(v6 + 1610616740);
    *(_DWORD *)(unsigned int)(v6 + 1610621028) = *(_DWORD *)(unsigned int)(v6 + 1610620836);
  }
  sub_650020A0(1610612996LL, 31, 1, 1);
  sub_650020A0(1610613004LL, 12, 1, 0);
  n16 = 16;
  if ( (unsigned int)sub_650020D0(0x600004E8u, 31, 1) )
    n128 = 128;
  else
    n128 = 64;
  n16_1 = 0;
  sub_650030B0(13, 2, n128);
  sub_650020F4(1LL);
  sub_650020A0(1610613004LL, 12, 1, 1);
  sub_650020A0(1610613076LL, 0, 1, 0);
  v10 = 192 * MEMORY[0x65010EB0];
  v11 = MEMORY[0x65010E78] + MEMORY[0x65010E70] - 4096LL;
  v12 = MEMORY[0x65010E78] + MEMORY[0x65010E70] - 16LL;
  sub_650020A0(1610613060LL, 16, 1, 1);
  while ( 1 )
  {
    sub_650020A0((unsigned int)(v10 + 1610616920), 0, 16, 1 << n16_1);
    sub_650020A0((unsigned int)(v10 + 1610621016), 0, 16, 1 << n16_1);
    v13 = sub_6500880C(1, 0LL, 0xFF0uLL);
    if ( MEMORY[0x65010E78] )
      v13 += sub_6500880C(1, v11, v12);
    if ( !v13 && n16 == 16 )
      n16 = n16_1;
    if ( n16 != 16 && v13 )
      break;
    if ( ++n16_1 == 16 )
      goto LABEL_20;
  }
  --n16_1;
LABEL_20:
  n0xF = (unsigned int)(n16 + n16_1) >> 1;
  if ( n0xF > 0xF )
    LOBYTE(n0xF) = 15;
  v15 = 1 << n0xF;
  sub_650020A0((unsigned int)(v10 + 1610616920), 0, 16, v15);
  sub_650020A0((unsigned int)(v10 + 1610621016), 0, 16, v15);
  return sub_650020A0(1610613060LL, 16, 1, 0);
}
// 650047AC: using guessed type __int64 __fastcall sub_650047AC(_QWORD);
// 65005138: using guessed type __int64 __fastcall sub_65005138(_QWORD);

//----- (00000000650072A8) ----------------------------------------------------
bool __fastcall sub_650072A8(int a1)
{
  _BOOL8 result; // x0
  int v3; // [xsp+24h] [xbp+24h] BYREF
  int v4; // [xsp+28h] [xbp+28h] BYREF
  int v5; // [xsp+2Ch] [xbp+2Ch] BYREF

  v3 = 0;
  v4 = 0;
  v5 = 0;
  sub_65003340(5, a1, &v3);
  if ( a1 )
    MEMORY[0x65010E84] = MEMORY[0x65010E84] & 0xFFFFFF00 | v3;
  else
    MEMORY[0x65010E80] = MEMORY[0x65010E80] & 0xFFFFFF00 | v3;
  sub_65003340(6, a1, &v4);
  if ( a1 )
    MEMORY[0x65010E84] = MEMORY[0x65010E84] & 0xFFFF00FF | (v4 << 8);
  else
    MEMORY[0x65010E80] = MEMORY[0x65010E80] & 0xFFFF00FF | (v4 << 8);
  sub_65003340(7, a1, &v5);
  if ( a1 )
    MEMORY[0x65010E84] = MEMORY[0x65010E84] & 0xFF00FFFF | (v5 << 16);
  else
    MEMORY[0x65010E80] = MEMORY[0x65010E80] & 0xFF00FFFF | (v5 << 16);
  result = 1LL;
  if ( v3 == v4 )
    return v3 != v5;
  return result;
}

//----- (00000000650073B0) ----------------------------------------------------
__int64 sub_650073B0()
{
  __int64 n1610621780; // x0
  __int64 v1; // x6
  unsigned int i; // w0
  __int64 v3; // x3
  unsigned int v4; // w1
  unsigned int *v5; // x3
  unsigned int n1610621780_1; // [xsp+2Ch] [xbp+2Ch] BYREF

  n1610621780_1 = 0;
  sub_65003340(0, 0, &n1610621780_1);
  if ( (n1610621780_1 & 2) != 0
    || (sub_65003340(0, 1, &n1610621780_1), n1610621780 = n1610621780_1, (n1610621780_1 & 2) != 0) )
  {
    sub_650020A0(1610613004LL, 16, 8, 255);
    v1 = MEMORY[0x65010E50];
    for ( i = 2; i < MEMORY[0x65010E68]; ++i )
    {
      v3 = v1 + 96LL * i;
      v4 = ((*(_DWORD *)(v3 + 40) & 0xFFFFFFE0) >> 8) & 0x1F | *(_DWORD *)(v3 + 40) & 0xFFFFFFE0;
      *(_DWORD *)(v3 + 40) = v4;
      v5 = (unsigned int *)(96 * i + 1610613144);
      *v5 = v4;
    }
    MEMORY[0x600011D4] = 4194800;
    MEMORY[0x600021D4] = 4194800;
    MEMORY[0x60001294] = -2143289296;
    MEMORY[0x60002294] = -2143289296;
    MEMORY[0x60001354] = -2143289296;
    n1610621780 = 1610621780LL;
    MEMORY[0x60002354] = -2143289296;
  }
  return n1610621780;
}

//----- (00000000650074E8) ----------------------------------------------------
__int64 __fastcall sub_650074E8(unsigned int n255_1, int a2)
{
  unsigned __int64 n255; // x19
  BOOL v3; // w0
  unsigned int v4; // w2
  __int64 n72; // x3
  __int64 v6; // x4
  __int64 n49152; // x2
  int v8; // w19
  int n24; // w3
  bool v10; // zf
  __int64 v11; // x2

  n255 = n255_1;
  v3 = sub_650072A8(a2);
  v4 = 0;
  if ( !v3 || (_DWORD)n255 == 255 )
    return v4;
  n72 = 0LL;
  v6 = (n255 >> 2) & 0xF;
  n49152 = 0LL;
  v8 = (unsigned __int8)n255 >> 6;
  do
  {
    if ( (_DWORD)v6 == *(_DWORD *)((char *)&unk_6500FE88 + n72) )
      n49152 = (unsigned int)(2 * *(_DWORD *)((char *)&unk_6500FE88 + n72 + 4));
    n72 += 8LL;
  }
  while ( n72 != 72 );
  if ( v8 == 1 )
    n49152 = (unsigned int)(2 * n49152);
  if ( (_DWORD)n49152 == 49152 )
  {
    n24 = 24;
    goto LABEL_28;
  }
  if ( (unsigned int)n49152 > 0xC000 )
  {
    if ( (_DWORD)n49152 == 0x20000 )
    {
      n24 = 64;
      goto LABEL_28;
    }
    if ( (unsigned int)n49152 > 0x20000 )
    {
      n24 = 96;
      if ( (_DWORD)n49152 == 196608 )
        goto LABEL_28;
      n24 = 128;
      v10 = (_DWORD)n49152 == 0x40000;
    }
    else
    {
      n24 = 32;
      if ( (_DWORD)n49152 == 0x10000 )
        goto LABEL_28;
      n24 = 48;
      v10 = (_DWORD)n49152 == 98304;
    }
    goto LABEL_23;
  }
  if ( (_DWORD)n49152 == 0x4000 )
  {
    n24 = 8;
    goto LABEL_28;
  }
  if ( (unsigned int)n49152 > 0x4000 )
  {
    n24 = 12;
    if ( (_DWORD)n49152 == 24576 )
      goto LABEL_28;
    n24 = 16;
    v10 = (_DWORD)n49152 == 0x8000;
LABEL_23:
    if ( !v10 )
      n24 = 0;
    goto LABEL_28;
  }
  n24 = 4;
  if ( (_DWORD)n49152 != 0x2000 )
  {
    n24 = 6;
    v10 = (_DWORD)n49152 == 12288;
    goto LABEL_23;
  }
LABEL_28:
  MEMORY[0x65010E5C] = ((n24 << (12 * a2 + 4)) | MEMORY[0x65010E5C] & ~(255 << (12 * a2 + 4))) & ~(15 << (12 * a2)) | (v8 << (12 * a2));
  v11 = n49152 << 16;
  if ( a2 )
  {
    MEMORY[0x65010E78] = v11;
    ++MEMORY[0x65010E6C];
  }
  else
  {
    MEMORY[0x65010E70] = v11;
  }
  return 1;
}

//----- (0000000065007678) ----------------------------------------------------
void *sub_65007678()
{
  void *result; // x0

  MEMORY[0x65010E58] = 65;
  MEMORY[0x65010E5C] = MEMORY[0x65010E5C] & 0xFFFFFF | 0x40000000;
  MEMORY[0x65010E68] = 8;
  MEMORY[0x65010E60] = 260;
  result = &unk_65010000;
  MEMORY[0x65010E90] = &unk_6500F648;
  return result;
}

//----- (0000000065007768) ----------------------------------------------------
__int64 sub_65007768()
{
  unsigned __int64 v0; // x1
  unsigned int n7; // w1
  unsigned __int8 v2; // w0
  char *v3; // x6
  __int64 v4; // x7
  __int64 result; // x0
  __int64 v6; // x1
  int v7; // w3
  int v8; // w3
  int *v9; // x1
  int n839965882; // w19
  int n275071504; // w20
  int v12; // w21
  int v13; // w3
  int n5; // w22

  if ( MEMORY[0x65010E5C] == 1074790912 )
    goto LABEL_56;
  if ( MEMORY[0x65010E5C] > 0x40100200u )
  {
    if ( MEMORY[0x65010E5C] <= 0x40201101u )
    {
      if ( MEMORY[0x65010E5C] >= 0x40201100u )
        goto LABEL_57;
      if ( MEMORY[0x65010E5C] == 1075315072 )
      {
LABEL_43:
        n839965882 = 839965882;
        n275071504 = -1739312624;
LABEL_58:
        v12 = 1;
        goto LABEL_59;
      }
      if ( MEMORY[0x65010E5C] <= 0x40180180u )
      {
        if ( MEMORY[0x65010E5C] != 1074794624 && MEMORY[0x65010E5C] != 1074794752 )
          goto LABEL_54;
        goto LABEL_45;
      }
      if ( MEMORY[0x65010E5C] == 1075839232 )
      {
LABEL_57:
        n839965882 = 839974620;
        n275071504 = -1164430832;
        goto LABEL_58;
      }
      if ( MEMORY[0x65010E5C] != 1075839488 )
      {
        if ( MEMORY[0x65010E5C] != 1075319169 )
          goto LABEL_54;
        goto LABEL_43;
      }
LABEL_56:
      n839965882 = 0;
      n275071504 = 0;
      v12 = 0;
LABEL_59:
      v13 = 0;
      n5 = 5;
      goto LABEL_60;
    }
    if ( MEMORY[0x65010E5C] != 1077937152 )
    {
      if ( MEMORY[0x65010E5C] <= 0x40400400u )
      {
        if ( MEMORY[0x65010E5C] == 1075843585 )
          goto LABEL_56;
        if ( MEMORY[0x65010E5C] != 1076892417 )
          goto LABEL_54;
        sub_650020A0(1610612992LL, 16, 3, 4);
        sub_650020A0(1610613052LL, 0, 2, 1);
        n839965882 = 839965882;
        sub_650020A0(1610613052LL, 4, 3, 6);
        n275071504 = -1739312624;
        v12 = 1;
LABEL_42:
        v13 = 0;
        n5 = 6;
        goto LABEL_60;
      }
      if ( MEMORY[0x65010E5C] != 1077941249 )
      {
        if ( MEMORY[0x65010E5C] == 1082136577 )
        {
          n839965882 = 0;
          sub_650020A0(1610612992LL, 16, 3, 7);
          n275071504 = 0;
          sub_650020A0(1610613052LL, 0, 2, 2);
          v12 = 0;
          sub_650020A0(1610613052LL, 4, 3, 2);
          v13 = 0;
          n5 = 7;
          goto LABEL_60;
        }
        goto LABEL_54;
      }
    }
    n839965882 = 0;
    sub_650020A0(1610612992LL, 16, 3, 4);
    n275071504 = 0;
    sub_650020A0(1610613052LL, 0, 2, 1);
    v12 = 0;
    sub_650020A0(1610613052LL, 4, 3, 6);
    goto LABEL_42;
  }
  if ( MEMORY[0x65010E5C] == 1073742336 )
  {
LABEL_44:
    n839965882 = 0;
    n275071504 = 0;
    v12 = 0;
    v13 = 1;
LABEL_47:
    n5 = 4;
    goto LABEL_60;
  }
  if ( MEMORY[0x65010E5C] > 0x40000200u )
  {
    if ( MEMORY[0x65010E5C] == 1074266240 )
    {
LABEL_49:
      n839965882 = 0;
      n275071504 = 0;
      v12 = 0;
      v13 = 0;
      goto LABEL_50;
    }
    if ( MEMORY[0x65010E5C] <= 0x40080080u )
    {
      if ( MEMORY[0x65010E5C] == 1074004032 )
      {
        n839965882 = 0;
        n275071504 = 0;
        v12 = 0;
        v13 = 0;
        goto LABEL_52;
      }
      if ( MEMORY[0x65010E5C] != 1074266176 )
      {
LABEL_54:
        sub_65002260("\r\ndetect type fail!!!");
        while ( 1 )
          ;
      }
      goto LABEL_49;
    }
    if ( MEMORY[0x65010E5C] != 1074790528 && MEMORY[0x65010E5C] != 1074790656 )
    {
      if ( MEMORY[0x65010E5C] != 1074528448 )
        goto LABEL_54;
      n839965882 = 1108370754;
      n275071504 = 275071504;
      v12 = 1;
      goto LABEL_46;
    }
LABEL_45:
    n839965882 = 0;
    n275071504 = 0;
    v12 = 0;
LABEL_46:
    v13 = 0;
    goto LABEL_47;
  }
  if ( MEMORY[0x65010E5C] == 1073742016 )
  {
LABEL_48:
    n839965882 = 0;
    n275071504 = 0;
    v12 = 0;
    v13 = 1;
LABEL_50:
    n5 = 3;
    goto LABEL_60;
  }
  if ( MEMORY[0x65010E5C] > 0x400000C0u )
  {
    if ( MEMORY[0x65010E5C] != 1073742080 )
    {
      if ( MEMORY[0x65010E5C] != 1073742208 )
        goto LABEL_54;
      goto LABEL_44;
    }
    goto LABEL_48;
  }
  if ( MEMORY[0x65010E5C] != 1073741888 )
  {
    if ( MEMORY[0x65010E5C] == 1073741952 )
    {
      n839965882 = 0;
      n275071504 = 0;
      v12 = 0;
      v13 = 1;
LABEL_52:
      n5 = 2;
      goto LABEL_60;
    }
    goto LABEL_54;
  }
  n839965882 = 0;
  v13 = 1;
  n275071504 = 0;
  v12 = 0;
  n5 = 1;
LABEL_60:
  sub_650020A0(1610612736LL, 14, 2, v13);
  sub_650020A0(1610612736LL, 0, 3, n5);
  sub_650020A0(1610612992LL, 4, 3, 2);
  sub_650020A0(1610612756LL, 3, 1, v12);
  MEMORY[0x60000018] = n275071504;
  MEMORY[0x6000001C] = n839965882;
  v0 = ((unsigned __int64)MEMORY[0x65010E84] >> 26) & 0xF;
  if ( (unsigned int)v0 < ((MEMORY[0x65010E80] >> 26) & 0xFu) )
    LODWORD(v0) = (MEMORY[0x65010E80] >> 26) & 0xF;
  n7 = v0 - 1;
  v2 = 0;
  if ( n7 <= 7 )
    v2 = byte_6500DDB0[n7];
  v3 = (char *)&unk_6500F7A8 + 32 * v2;
  v4 = MEMORY[0x65010E50];
  result = 0LL;
  while ( (unsigned int)result < MEMORY[0x65010E68] )
  {
    v6 = v4 + 96LL * (unsigned int)result;
    v7 = *(_DWORD *)(v6 + 24) & 0xFC00;
    *(_DWORD *)(v6 + 24) = v7;
    v8 = v7 | *(_DWORD *)&v3[4 * (unsigned int)result];
    *(_DWORD *)(v6 + 24) = v8;
    v9 = (int *)(unsigned int)(96 * result + 1610613128);
    result = (unsigned int)(result + 1);
    *v9 = v8;
  }
  return result;
}
// 6500DDB0: using guessed type _BYTE byte_6500DDB0[200];

//----- (0000000065007C30) ----------------------------------------------------
__int64 sub_65007C30()
{
  unsigned int n255; // [xsp+3Ch] [xbp+3Ch] BYREF

  n255 = 0;
  MEMORY[0x65010E70] = 0LL;
  MEMORY[0x65010E78] = 0LL;
  MEMORY[0x65010E6C] = 1;
  MEMORY[0x65010E80] = 0;
  MEMORY[0x65010E84] = 0;
  if ( (unsigned int)sub_65003340(8, 0, &n255) )
  {
    sub_65002260("CS0 mr8 read fail!!!\n");
    while ( 1 )
      ;
  }
  MEMORY[0x65010E80] = MEMORY[0x65010E80] & 0xFFFFFF | (n255 << 24);
  if ( !(unsigned int)sub_650074E8(n255, 0) )
  {
    sub_65002260("ddr auto detect cs0 fail!!!\n");
    while ( 1 )
      ;
  }
  n255 = 0;
  sub_65003340(8, 1, &n255);
  MEMORY[0x65010E84] = MEMORY[0x65010E84] & 0xFFFFFF | (n255 << 24);
  if ( !(unsigned int)sub_650074E8(n255, 1) )
    sub_65002260("ddr auto detect cs1 unvalid!!!\n");
  sub_650073B0();
  sub_650020A0(1610612996LL, 28, 1, 0);
  return sub_65007768();
}

//----- (0000000065007D30) ----------------------------------------------------
__int64 sub_65007D30()
{
  __int64 result; // x0

  MEMORY[0x82000028] = MEMORY[0x65010E70];
  MEMORY[0x82000020] = MEMORY[0x65010E6C];
  MEMORY[0x82000030] = MEMORY[0x65010E78];
  if ( MEMORY[0x65010E6C] == 1 )
  {
    MEMORY[0x82000008] = MEMORY[0x65010E70];
    MEMORY[0x82000030] = 0LL;
    MEMORY[0x82000028] = MEMORY[0x65010E70] - 4096LL;
  }
  else if ( MEMORY[0x65010E6C] == 2 )
  {
    MEMORY[0x82000008] = MEMORY[0x65010E78] + MEMORY[0x65010E70];
    MEMORY[0x82000030] = MEMORY[0x65010E78] - 4096LL;
  }
  result = 2181038080LL;
  MEMORY[0x82000038] = MEMORY[0x65010E80];
  return result;
}

//----- (0000000065007D9C) ----------------------------------------------------
_DWORD *__fastcall sub_65007D9C(_DWORD *result)
{
  *result = 1866;
  return result;
}

//----- (0000000065007DA8) ----------------------------------------------------
__int64 sub_65007DA8()
{
  int n35; // w1
  __int64 n1610622780; // x0

  if ( MEMORY[0x65010E60] == 4 )
  {
    MEMORY[0x60001678] = 5386560;
    MEMORY[0x6000167C] = 4622488;
    MEMORY[0x60002678] = 2437440;
    MEMORY[0x6000267C] = 4622488;
    MEMORY[0x600016F4] = 376456488;
    MEMORY[0x600016F8] = 1445167476;
    MEMORY[0x600016FC] = 3;
    MEMORY[0x60001734] = 1985226768;
    MEMORY[0x60001738] = 1985250064;
    n35 = 35;
    MEMORY[0x6000173C] = 35;
    MEMORY[0x600026F4] = 1450324993;
    MEMORY[0x600026F8] = 1450324993;
    MEMORY[0x600026FC] = 136;
    MEMORY[0x60002734] = 1702111248;
    MEMORY[0x60002738] = 1466204944;
    n1610622780 = 1610622780LL;
  }
  else
  {
    if ( MEMORY[0x65010E60] != 260 )
    {
      while ( 1 )
        ;
    }
    MEMORY[0x60001678] = 4534305;
    MEMORY[0x6000167C] = 4620696;
    MEMORY[0x60002678] = 5517345;
    MEMORY[0x6000267C] = 4620696;
    MEMORY[0x600016F4] = 675505936;
    MEMORY[0x600016F8] = 595937040;
    MEMORY[0x600016FC] = 101;
    MEMORY[0x60001734] = 1983984256;
    MEMORY[0x60001738] = 1988440640;
    MEMORY[0x6000173C] = 21;
    MEMORY[0x600026F4] = 20473682;
    MEMORY[0x600026F8] = 588796007;
    MEMORY[0x600026FC] = 68;
    MEMORY[0x60002734] = 1698771584;
    MEMORY[0x60002738] = -2023411136;
    n1610622780 = 1610622780LL;
    n35 = 23;
  }
  MEMORY[0x6000273C] = n35;
  return n1610622780;
}

//----- (0000000065008028) ----------------------------------------------------
__int64 __fastcall sub_65008028(int a1)
{
  sub_650020A0(1610616836LL, 0, 3, a1);
  sub_650020A0(1610616836LL, 4, 3, a1);
  sub_650020A0(1610620932LL, 0, 3, a1);
  return sub_650020A0(1610620932LL, 4, 3, a1);
}

//----- (00000000650080F4) ----------------------------------------------------
__int64 sub_650080F4()
{
  __int64 n1610953728; // x0

  sub_650020A0(1610956800LL, 23, 1, 1);
  sub_650020A0(1610952964LL, 8, 8, 0);
  n1610953728 = 1610953728LL;
  MEMORY[0x60053400] = 19;
  return n1610953728;
}

//----- (0000000065008144) ----------------------------------------------------
__int64 sub_65008144()
{
  __int64 n1610956800; // x0

  MEMORY[0x60053400] = 0;
  MEMORY[0x60053104] |= 0xFF00u;
  n1610956800 = 1610956800LL;
  MEMORY[0x60054000] &= ~0x800000u;
  return n1610956800;
}

//----- (000000006500818C) ----------------------------------------------------
unsigned __int64 __fastcall sub_6500818C(int a1)
{
  unsigned __int64 result; // x0

  *(_DWORD *)(unsigned int)((a1 << 14) + 1611071488) &= ~2u;
  *(_DWORD *)(unsigned int)((a1 << 14) + 1611071488) |= 4u;
  result = (unsigned int)((a1 << 14) + 1611071488);
  *(_DWORD *)result &= ~4u;
  return result;
}

//----- (00000000650081E4) ----------------------------------------------------
unsigned __int64 __fastcall sub_650081E4(int a1)
{
  unsigned __int64 result; // x0

  result = (unsigned int)((a1 << 14) + 1611071488);
  *(_DWORD *)result |= 2u;
  return result;
}

//----- (0000000065008214) ----------------------------------------------------
__int64 __fastcall sub_65008214(int a1)
{
  __int64 result; // x0
  int v2; // [xsp+Ch] [xbp-4h]

  v2 = a1 << 14;
  do
    result = *(unsigned int *)(unsigned int)(v2 + 1611071664);
  while ( (result & 2) == 0 );
  return result;
}

//----- (0000000065008240) ----------------------------------------------------
__int64 __fastcall sub_65008240(int a1)
{
  int v2; // [xsp+Ch] [xbp-4h]

  v2 = a1 << 14;
  while ( (*(_DWORD *)(unsigned int)(v2 + 1611071664) & 2) == 0 )
    ;
  return *(_DWORD *)(unsigned int)(v2 + 1611071664) & 1;
}

//----- (0000000065008288) ----------------------------------------------------
__int64 __fastcall sub_65008288(int a1, int a2, int n2, int a4, int a5, __int64 a6)
{
  __int64 j_1; // x0
  unsigned int j; // [xsp+58h] [xbp+58h]
  unsigned int k; // [xsp+58h] [xbp+58h]
  unsigned int i; // [xsp+58h] [xbp+58h]
  int v15; // [xsp+5Ch] [xbp+5Ch]

  v15 = a1 << 14;
  sub_6500818C(a1);
  *(_DWORD *)(unsigned int)(v15 + 1611071488) = 0;
  *(_DWORD *)(unsigned int)(v15 + 1611071488) |= 1u;
  sub_650020A0((unsigned int)(v15 + 1611071488), 3, 2, a2);
  sub_650020A0((unsigned int)(v15 + 1611071488), 5, 2, n2);
  *(_DWORD *)(unsigned int)(v15 + 1611071488) |= 0x780u;
  sub_650020A0((unsigned int)(v15 + 1611071488), 11, 3, a4);
  *(_DWORD *)(unsigned int)(v15 + 1611071488) &= ~0x4000u;
  *(_DWORD *)(unsigned int)(v15 + 1611071488) |= 0x3000000u;
  *(_DWORD *)(unsigned int)(v15 + 1611071488) &= ~0x10000000u;
  *(_DWORD *)(unsigned int)(v15 + 1611071492) = a5;
  sub_650020A0((unsigned int)(v15 + 1611071664), 24, 8, SHIDWORD(a6));
  j_1 = (unsigned int)(v15 + 1611071496);
  *(_DWORD *)j_1 = a6;
  if ( n2 == 1 )
  {
    for ( i = 0; i <= 7; ++i )
      *(_DWORD *)(v15 + 4 * (i + 402767876)) = dword_6500FED0[i];
    *(_DWORD *)(unsigned int)(v15 + 1611071528) = 19088743;
    j_1 = 2309737967LL;
    *(_DWORD *)(unsigned int)(v15 + 1611071532) = -1985229329;
  }
  else if ( n2 )
  {
    if ( n2 == 2 )
    {
      for ( j = 0; ; ++j )
      {
        j_1 = j;
        if ( j > 3 )
          break;
        *(_DWORD *)(v15 + 4 * (j + 402767923)) = dword_6500FEF0[j];
      }
    }
  }
  else
  {
    for ( k = 0; ; ++k )
    {
      j_1 = k;
      if ( k > 0x1F )
        break;
      *(_DWORD *)(v15 + 4 * (k + 402767884)) = *(_DWORD *)(0x65010EF0 + 4LL * k);
    }
  }
  return j_1;
}
// 6500FED0: using guessed type _DWORD dword_6500FED0[8];
// 6500FEF0: using guessed type _DWORD dword_6500FEF0[4];

//----- (0000000065008520) ----------------------------------------------------
unsigned __int64 __fastcall sub_65008520(int a1)
{
  sub_650080F4();
  sub_6500818C(a1);
  return sub_650081E4(a1);
}

//----- (000000006500854C) ----------------------------------------------------
__int64 __fastcall sub_6500854C(unsigned int *a1, __int64 a2, __int64 a3)
{
  unsigned __int64 v3; // x2
  unsigned int v6; // w20
  unsigned __int64 v7; // x4
  unsigned __int64 v8; // x22
  int v9; // w23
  unsigned int i; // w20
  unsigned int v11; // w21
  unsigned int j; // w20

  v3 = a3 - a2;
  if ( !*a1 )
    *a1 = 1;
  v6 = 0;
  v7 = (v3 / *a1) >> 8;
  v8 = v7 << 8;
  v9 = v7 - 1;
  while ( v6 < *a1 )
  {
    sub_65008288(v6, a1[1], a1[3], a1[2], v9, a2 + (unsigned __int8)v6 * v8);
    v6 = (unsigned __int8)(v6 + 1);
  }
  for ( i = 0; i < *a1; i = (unsigned __int8)(i + 1) )
    sub_65008520(i);
  v11 = 0;
  for ( j = 0; j < *a1; j = (unsigned __int8)(j + 1) )
  {
    sub_65008214(j);
    v11 = (unsigned __int8)(v11 + sub_65008240(j));
    sub_6500818C(j);
  }
  sub_65008144();
  return v11;
}

//----- (0000000065008650) ----------------------------------------------------
__int64 __fastcall sub_65008650(unsigned __int64 a1, unsigned __int64 a2)
{
  __int64 v3; // x21

  MEMORY[0x65010EF8] = 1;
  if ( MEMORY[0x65010E6C] == 2 )
  {
    v3 = MEMORY[0x65010E78] + MEMORY[0x65010E70];
  }
  else
  {
    v3 = 0LL;
    if ( MEMORY[0x65010E6C] == 1 )
      v3 = MEMORY[0x65010E70];
  }
  if ( a1 <= a2 )
  {
    MEMORY[0x65010F18] = 0LL;
    MEMORY[0x65010F20] = a1;
    if ( (unsigned __int8)sub_6500854C((unsigned int *)0x65010EF8, 0LL, a1) )
    {
      sub_65002260("dram_bist_all_space bist error\r\n");
      while ( 1 )
        ;
    }
    MEMORY[0x65010F18] = a2;
    MEMORY[0x65010F20] = v3 - 1;
    if ( a2 < v3 - 2 && (unsigned __int8)sub_6500854C((unsigned int *)0x65010EF8, a2, v3 - 1) )
    {
      sub_65002260("dram_bist_all_space bist error\r\n");
      while ( 1 )
        ;
    }
    return 0LL;
  }
  else
  {
    sub_65002260("Error:dram_bist_all_space : except_start > except_end !! ");
    return 0xFFFFFFFFLL;
  }
}

//----- (000000006500880C) ----------------------------------------------------
__int64 __fastcall sub_6500880C(int a1, unsigned __int64 a2, unsigned __int64 a3)
{
  __int64 v3; // x4
  unsigned __int64 v4; // x4

  if ( !a1 )
    return sub_65008650(a2, a3);
  if ( a1 == 1 )
  {
    MEMORY[0x65010EF8] = 3;
    MEMORY[0x65010EFC] = 2;
    MEMORY[0x65010F04] = 1;
    MEMORY[0x65010F00] = 4;
    if ( MEMORY[0x65010E6C] == 2 )
    {
      v3 = MEMORY[0x65010E78] + MEMORY[0x65010E70];
    }
    else
    {
      v3 = 0LL;
      if ( MEMORY[0x65010E6C] == 1 )
        v3 = MEMORY[0x65010E70];
    }
    if ( a2 <= a3 && (v4 = v3 - 1, a2 <= v4) && a3 <= v4 )
    {
      MEMORY[0x65010F08] = a2;
      MEMORY[0x65010F10] = a3;
      return (unsigned __int8)sub_6500854C((unsigned int *)0x65010EF8, a2, a3);
    }
    else
    {
      sub_65002260("Error:dram_bist_specific_space : start > end or scan address > dram_size\n");
      return 1LL;
    }
  }
  else
  {
    sub_65002260("[ddrc] bist_mode error!!\r\n");
    return 0xFFFFFFFFLL;
  }
}

//----- (0000000065008854) ----------------------------------------------------
__int64 __fastcall sub_65008854(int a1)
{
  int n200000; // w1

  MEMORY[0x64400028] = a1;
  n200000 = 200000;
  while ( (MEMORY[0x6440002C] & 0x80000000) != 0 )
  {
    if ( !--n200000 )
      return 0xFFFFFFFFLL;
  }
  return MEMORY[0x6440002C];
}

//----- (0000000065008890) ----------------------------------------------------
__int64 __fastcall sub_65008890(unsigned int a1, unsigned __int16 a2)
{
  unsigned __int64 v2; // x20
  int v3; // w21
  int n1000; // w19
  int n200000; // w19
  int n2000; // w20

  v2 = a1;
  v3 = a2;
  n1000 = 1000;
  while ( (MEMORY[0x64400030] & 0x400) == 0 )
  {
    if ( !--n1000 )
      return 0xFFFFFFFFLL;
    sub_6500A70C(2);
  }
  n200000 = 200000;
  while ( (MEMORY[0x64400030] & 0x800) != 0 )
  {
    if ( !--n200000 )
      return 0xFFFFFFFFLL;
  }
  *(_DWORD *)v2 = v3;
  n2000 = 2000;
  while ( (MEMORY[0x64400030] & 0x80000000) != 0 )
  {
    sub_6500A70C(1);
    if ( !--n2000 )
      return 0xFFFFFFFFLL;
  }
  return 0LL;
}
// 65008914: conditional instruction was optimized away because w20.4!=0
// 6500891C: conditional instruction was optimized away because w19.4!=0

//----- (0000000065008940) ----------------------------------------------------
__int64 __fastcall sub_65008940(int a1)
{
  if ( (unsigned int)(a1 - 1682046976) > 0x20000 )
    return 0xFFFFFFFFLL;
  else
    return sub_65008854(a1);
}

//----- (000000006500895C) ----------------------------------------------------
__int64 __fastcall sub_6500895C(unsigned int a1, unsigned __int16 a2)
{
  if ( a1 - 1682046976 > 0x20000 )
    return 0xFFFFFFFFLL;
  else
    return sub_65008890(a1, a2);
}

//----- (000000006500897C) ----------------------------------------------------
__int64 __fastcall sub_6500897C(unsigned int a1, __int16 a2, __int16 a3)
{
  __int64 result; // x0

  if ( a1 - 1682046976 > 0x20000 )
    return 0xFFFFFFFFLL;
  result = sub_65008854(a1);
  if ( (result & 0x80000000) == 0 )
    return sub_65008890(a1, (unsigned __int16)(result & ~a3) | a2);
  return result;
}

//----- (00000000650089EC) ----------------------------------------------------
__int64 __fastcall sub_650089EC(_DWORD *a1)
{
  __int64 n1681915908; // x0

  MEMORY[0x64900008] = *a1 | 0x200;
  MEMORY[0x64900014] |= 0x200u;
  sub_6500A70C(2);
  MEMORY[0x64900014] &= ~0x200u;
  MEMORY[0x64400008] = 0;
  MEMORY[0x6440000C] = 0;
  n1681915908 = 1681915908LL;
  MEMORY[0x64400004] = MEMORY[0x64400004] & 0xFFFFFF60 | 1;
  return n1681915908;
}

//----- (0000000065008A8C) ----------------------------------------------------
__int64 __fastcall sub_65008A8C(_DWORD *a1, unsigned __int8 a2)
{
  unsigned int v2; // w2

  v2 = 0;
  if ( (unsigned int)a2 < *a1 )
    return *(unsigned int *)(a1[11] + 4 * (unsigned int)a2 + a1[10]);
  return v2;
}

//----- (0000000065008ABC) ----------------------------------------------------
__int64 __fastcall sub_65008ABC(__int64 a1, unsigned int a2)
{
  __int64 v2; // x3
  __int64 v3; // x1
  __int64 result; // x0

  v2 = *(_QWORD *)(a1 + 16);
  v3 = 2LL * a2;
  result = 0LL;
  if ( *(_BYTE *)(v2 + v3 + 1) )
    return (0xFFFFFFFF >> (32 - *(_BYTE *)(v2 + v3 + 1) - (*(_BYTE *)(v2 + v3) & 0x1F))) & (-1 << (*(_BYTE *)(v2 + v3) & 0x1F));
  return result;
}

//----- (0000000065008AFC) ----------------------------------------------------
__int64 __fastcall sub_65008AFC(__int64 a1)
{
  __int64 v1; // x2
  int v2; // w3
  int n3; // w19
  unsigned int v5; // w20
  int v6; // w0
  unsigned int n3_1; // w20
  _QWORD v9[4]; // [xsp+30h] [xbp+30h]

  v9[2] = 13LL;
  v9[3] = 26LL;
  v1 = *(_QWORD *)(a1 + 16);
  v2 = *(unsigned __int8 *)(v1 + 9);
  v9[0] = 2LL;
  v9[1] = 4LL;
  n3 = 3;
  if ( v2 )
  {
    v5 = *(unsigned __int8 *)(v1 + 8);
    v6 = sub_65008ABC(a1, 4u);
    n3_1 = ((unsigned int)sub_65008A8C((_DWORD *)a1, v5 >> 5) & v6) >> (v5 & 0x1F);
    if ( n3_1 > 3 )
      n3 = 3;
    else
      n3 = n3_1;
  }
  return v9[n3];
}

//----- (0000000065008B84) ----------------------------------------------------
__int64 __fastcall sub_65008B84(__int64 a1, unsigned __int64 n0x18CBA80_9)
{
  int v3; // w25
  unsigned __int64 v4; // x21
  int v5; // w0
  __int64 v6; // x2
  unsigned int v7; // w22
  __int64 v8; // x0
  unsigned __int8 v9; // w23
  int v10; // w3
  __int64 v11; // x24
  unsigned __int64 n0x18CBA80_10; // x2
  char v13; // w23
  unsigned __int64 n0x18CBA80_11; // x0
  unsigned __int64 n0x18CBA80_3; // x1
  __int64 v16; // x23
  int v17; // w0
  unsigned int v18; // w2
  int v19; // w22
  unsigned int v20; // w24
  char v21; // w23
  int v22; // w0
  __int64 v23; // x2
  _DWORD *v24; // x0
  unsigned __int64 n0x18CBA80_5; // x22
  __int64 v26; // x23
  unsigned int i; // w0
  unsigned int *v28; // x2
  unsigned int v29; // w26
  int v30; // w0
  _BYTE *v31; // x24
  unsigned __int8 v32; // w2
  _DWORD *v33; // x3
  int v34; // w0
  unsigned int v35; // w2
  unsigned int v36; // w1
  unsigned int v37; // w4
  char v38; // w5
  __int64 v39; // x2
  _DWORD *v40; // x3
  unsigned int n0x18CBA80; // w1
  unsigned int n0x18CBA80_6; // w24
  unsigned __int64 n0x18CBA80_7; // x23
  _BYTE *v44; // x22
  int v45; // w0
  _DWORD *v46; // x3
  int v47; // w0
  _DWORD *v48; // x2
  int v49; // w0
  _DWORD *v50; // x2
  int v51; // w0
  _DWORD *v52; // x2
  int v53; // w0
  _DWORD *v54; // x2
  int v55; // w0
  _DWORD *v56; // x3
  unsigned int v57; // w0
  unsigned int v58; // w26
  unsigned int v59; // w24
  unsigned int v60; // w27
  unsigned int n0x18CBA80_1; // w1
  char v62; // w26
  unsigned int v63; // w2
  unsigned int v64; // w23
  unsigned __int64 n0x18CBA80_12; // x21
  _DWORD *v66; // x1
  __int64 v67; // x21
  int v68; // w22
  unsigned int v69; // w23
  int v70; // w0
  int *v71; // x2
  unsigned __int64 n0x18CBA80_4; // [xsp+68h] [xbp+68h] BYREF
  unsigned __int64 n0x18CBA80_2; // [xsp+70h] [xbp+70h] BYREF
  unsigned __int64 n0x18CBA80_8; // [xsp+78h] [xbp+78h] BYREF
  _BYTE v76[32]; // [xsp+80h] [xbp+80h] BYREF

  n0x18CBA80_2 = n0x18CBA80_9;
  v3 = *(_DWORD *)a1;
  sub_65002000((__int64)v76, 0, 32LL);
  v4 = sub_65008AFC(a1);
  v5 = sub_65008ABC(a1, 9u);
  v6 = *(_QWORD *)(a1 + 16);
  if ( *(_BYTE *)(v6 + 19) && ((unsigned int)sub_65008A8C((_DWORD *)a1, *(_BYTE *)(v6 + 18) >> 5) & v5) != 0 )
    v4 *= 2LL;
  v7 = sub_65008ABC(a1, 0xAu);
  v8 = *(_QWORD *)(a1 + 16);
  v9 = *(_BYTE *)(v8 + 20);
  v10 = *(unsigned __int8 *)(v8 + 21);
  v11 = v9 >> 5;
  *(_DWORD *)&v76[8 * v11 + 4] |= v7;
  if ( !v10 )
    goto LABEL_18;
  if ( *(_WORD *)(a1 + 30) == 1 )
  {
    n0x18CBA80_10 = *(_QWORD *)(a1 + 32);
    if ( n0x18CBA80_2 > n0x18CBA80_10 || !n0x18CBA80_2 )
      goto LABEL_18;
    n0x18CBA80_4 = *(_QWORD *)(a1 + 32);
    if ( HIDWORD(n0x18CBA80_10) )
      sub_65009160(&n0x18CBA80_4, n0x18CBA80_2);
    else
      n0x18CBA80_4 = (unsigned int)n0x18CBA80_10 / (unsigned int)n0x18CBA80_2;
    v13 = v9 & 0x1F;
    n0x18CBA80_11 = n0x18CBA80_4;
    if ( n0x18CBA80_4 > v7 >> v13 )
      return (unsigned int)-1;
    *(_DWORD *)&v76[8 * v11] |= v7 & (n0x18CBA80_4 << v13);
    n0x18CBA80_3 = n0x18CBA80_2 * (n0x18CBA80_11 + 1);
    goto LABEL_17;
  }
  if ( !*(_WORD *)(a1 + 30) )
  {
    if ( n0x18CBA80_2 <= *(_QWORD *)(a1 + 32) )
    {
      n0x18CBA80_3 = 2 * n0x18CBA80_2;
LABEL_17:
      n0x18CBA80_2 = n0x18CBA80_3;
      goto LABEL_18;
    }
    *(_DWORD *)&v76[8 * v11] |= v7;
  }
LABEL_18:
  v16 = *(_QWORD *)(a1 + 16);
  if ( *(_BYTE *)(v16 + 23) )
  {
    v17 = sub_65008ABC(a1, 0xBu);
    v18 = *(unsigned __int8 *)(v16 + 22);
    v19 = v17;
    n0x18CBA80_4 = n0x18CBA80_2;
    v20 = v18 >> 5;
    v21 = v18 & 0x1F;
    if ( HIDWORD(n0x18CBA80_2) )
    {
      v22 = sub_65009160(&n0x18CBA80_4, 0x18CBA80u);
    }
    else
    {
      v22 = (unsigned int)n0x18CBA80_2 % 0x18CBA80;
      n0x18CBA80_4 = (unsigned int)n0x18CBA80_2 / 0x18CBA80;
    }
    v23 = 0LL;
    if ( v22 )
    {
      v4 >>= 1;
      v23 = 1LL;
    }
    v24 = &v76[8 * (v20 & 7)];
    *v24 |= v19 & (v23 << v21);
    v24[1] |= v19;
  }
  n0x18CBA80_5 = n0x18CBA80_2;
  v26 = *(_QWORD *)(a1 + 8);
  for ( i = 0; ; ++i )
  {
    v28 = (unsigned int *)(v26 + 24LL * i);
    v29 = *v28;
    if ( *v28 > 0xFE || n0x18CBA80_2 <= *((_QWORD *)v28 + 1) )
      break;
  }
  v30 = sub_65008ABC(a1, 5u);
  v31 = *(_BYTE **)(a1 + 16);
  v32 = v31[10];
  v33 = &v76[8 * (v32 >> 5)];
  *v33 |= (v29 << (v32 & 0x1F)) & v30;
  v33[1] |= v30;
  if ( v31[25] )
  {
    v34 = sub_65008ABC(a1, 0xCu);
    v35 = (unsigned __int8)v31[24];
    v36 = 0;
    v37 = v35 >> 5;
    v38 = v35 & 0x1F;
    while ( 1 )
    {
      v39 = v26 + 24LL * v36;
      if ( *(_DWORD *)v39 > 0xFEu || n0x18CBA80_5 <= *(_QWORD *)(v39 + 8) )
        break;
      ++v36;
    }
    v40 = &v76[8 * (v37 & 7)];
    *v40 |= v34 & ((unsigned __int64)*(unsigned int *)(v39 + 16) << v38);
    v40[1] |= v34;
  }
  n0x18CBA80 = 1000000 * v4;
  if ( HIDWORD(n0x18CBA80_5) )
  {
    n0x18CBA80_6 = sub_65009160(&n0x18CBA80_2, n0x18CBA80);
  }
  else
  {
    n0x18CBA80_2 = (unsigned int)n0x18CBA80_5 / n0x18CBA80;
    n0x18CBA80_6 = (unsigned int)n0x18CBA80_5 % n0x18CBA80;
  }
  n0x18CBA80_4 = n0x18CBA80_6;
  n0x18CBA80_7 = n0x18CBA80_2;
  v44 = *(_BYTE **)(a1 + 16);
  if ( n0x18CBA80_6 )
  {
    v51 = sub_65008ABC(a1, 1u);
    v52 = &v76[8 * (v44[2] >> 5)];
    *v52 |= v51;
    v52[1] |= v51;
    v53 = sub_65008ABC(a1, 3u);
    v54 = &v76[8 * (v44[6] >> 5)];
    *v54 |= v53;
    v54[1] |= v53;
    v55 = sub_65008ABC(a1, 7u);
    v56 = &v76[8 * (v44[14] >> 5)];
    *v56 |= v55 & (n0x18CBA80_7 << (v44[14] & 0x1F));
    v56[1] |= v55;
    v57 = sub_65008ABC(a1, 8u);
    v58 = (unsigned __int8)v44[16];
    v59 = n0x18CBA80_6 / 0x2710;
    v60 = v58 >> 5;
    n0x18CBA80_1 = 100 * v4;
    v62 = v58 & 0x1F;
    v63 = (v57 >> v62) + 1;
    v64 = v57;
    n0x18CBA80_12 = ((100 * v4) >> 1) + v63 * (unsigned __int64)v59;
    n0x18CBA80_4 = v63 * (unsigned __int64)v59;
    n0x18CBA80_8 = n0x18CBA80_12;
    if ( HIDWORD(n0x18CBA80_12) )
      sub_65009160(&n0x18CBA80_8, n0x18CBA80_1);
    else
      n0x18CBA80_8 = (unsigned int)n0x18CBA80_12 / n0x18CBA80_1;
    v66 = &v76[8 * (v60 & 7)];
    *v66 |= v64 & (n0x18CBA80_8 << v62);
    v66[1] |= v64;
  }
  else
  {
    v45 = sub_65008ABC(a1, 6u);
    v46 = &v76[8 * (v44[12] >> 5)];
    *v46 |= v45 & (n0x18CBA80_7 << (v44[12] & 0x1F));
    v46[1] |= v45;
    v47 = sub_65008ABC(a1, 1u);
    v48 = &v76[8 * (v44[2] >> 5)];
    *v48 &= ~v47;
    v48[1] |= v47;
    v49 = sub_65008ABC(a1, 3u);
    v50 = &v76[8 * (v44[6] >> 5)];
    *v50 &= ~v49;
    v50[1] |= v49;
  }
  v67 = 0LL;
  v68 = 0;
  v69 = 0;
  while ( v68 != v3 )
  {
    v70 = *(_DWORD *)&v76[v67 + 4];
    if ( v70 )
    {
      if ( (unsigned int)(unsigned __int8)v68 < *(_DWORD *)a1 )
      {
        v71 = (int *)(*(_DWORD *)(a1 + 44) + 4 * (unsigned int)(unsigned __int8)v68 + *(_DWORD *)(a1 + 40));
        *v71 = *v71 & ~v70 | *(_DWORD *)&v76[v67];
      }
      if ( ((unsigned int)sub_65008A8C((_DWORD *)a1, v68) & *(_DWORD *)&v76[v67 + 4]) != *(_DWORD *)&v76[v67] )
        v69 = -1;
    }
    ++v68;
    v67 += 8LL;
  }
  if ( !v69 )
    sub_6500A70C(*(unsigned __int16 *)(a1 + 24));
  return v69;
}

//----- (0000000065009160) ----------------------------------------------------
__int64 __fastcall sub_65009160(unsigned __int64 *a1, unsigned int a2)
{
  unsigned __int64 v2; // x2
  unsigned __int64 v3; // x3
  unsigned __int64 v4; // x5
  unsigned __int64 v5; // x4
  __int64 v6; // x5
  unsigned __int64 v7; // x1

  v2 = *a1;
  v3 = a2;
  v4 = HIDWORD(*a1);
  v5 = 0LL;
  if ( (unsigned int)v4 >= a2 )
  {
    v6 = (unsigned int)v4 / a2;
    v5 = v6 << 32;
    v2 -= (unsigned __int64)((unsigned int)v6 * a2) << 32;
  }
  v7 = 1LL;
  while ( (__int64)v3 > 0 && v3 < v2 )
  {
    v3 *= 2LL;
    v7 *= 2LL;
  }
  do
  {
    if ( v2 >= v3 )
    {
      v2 -= v3;
      v5 += v7;
    }
    v7 >>= 1;
    v3 >>= 1;
  }
  while ( v7 );
  *a1 = v5;
  return (unsigned int)v2;
}

//----- (00000000650091D0) ----------------------------------------------------
// write access to const memory has been detected, the output may be wrong!
bool sub_650091D0()
{
  BOOL v0; // w19
  unsigned __int16 v1; // w2
  int v3; // [xsp+2Ch] [xbp+2Ch] BYREF

  v3 = 0;
  v0 = sub_6500B594(7u, 7u, (__int64)&v3, 1);
  if ( (v3 & 0x10000) == 0 || (MEMORY[0x64890008] & 0xF) == 0xF )
  {
    sub_650021A0("\r\ndebug enable!\r\n");
    loc_650043FC = -976894482;
    v1 = -14866;
  }
  else
  {
    sub_650021A0("\r\ndebug disable!\r\n");
    loc_650043FC = -976894499;
    v1 = -14883;
  }
  loc_650053FC = v1 | 0xC5C50000;
  return v0;
}
// 65009234: write access to const memory at 650043FC has been detected
// 65009264: write access to const memory at 650043FC has been detected
// 65009278: write access to const memory at 650053FC has been detected

//----- (000000006500928C) ----------------------------------------------------
_DWORD *__fastcall sub_6500928C(_QWORD *a1, __int16 a2)
{
  _DWORD *result; // x0
  int n301924352; // [xsp+Ch] [xbp-4h]

  n301924352 = 0;
  if ( (a2 & 1) != 0 )
    n301924352 = 301924352;
  if ( (a2 & 2) != 0 )
    n301924352 |= 0x100u;
  if ( (a2 & 4) != 0 )
    n301924352 |= 0x40u;
  if ( (a2 & 8) != 0 )
    n301924352 |= 0x80u;
  if ( (a2 & 0x10) != 0 )
    n301924352 |= 0x20u;
  if ( (a2 & 0x20) != 0 )
    n301924352 |= 0x10u;
  if ( (a2 & 0x40) != 0 )
    n301924352 |= 8u;
  if ( (a2 & 0x80) != 0 )
    n301924352 |= 4u;
  if ( (a2 & 0x100) != 0 )
    n301924352 |= 2u;
  if ( (a2 & 0x200) != 0 )
    n301924352 |= 1u;
  result = (_DWORD *)*a1;
  result[14] &= ~n301924352;
  result[13] &= ~n301924352;
  result[12] = n301924352;
  return result;
}

//----- (000000006500936C) ----------------------------------------------------
__int64 __fastcall sub_6500936C(_DWORD *a1, int a2)
{
  unsigned int v2; // w3
  unsigned int v3; // w1
  unsigned int v4; // w2
  unsigned int v5; // w1
  __int64 v6; // x4
  unsigned int v7; // w3
  __int64 result; // x0
  unsigned int v9; // [xsp+Ch] [xbp-4h]

  v2 = a1[3];
  v3 = 2 * a2;
  v4 = 0;
  if ( v2 >= v3 )
  {
    v4 = v2 / v3;
    if ( v2 / (v2 / v3) > v3 )
      ++v4;
  }
  v5 = v4 >> 1;
  if ( (v4 & 1) != 0 )
    v5 = (v4 + 1) >> 1;
  v6 = *(_QWORD *)a1;
  v9 = ((unsigned __int8)v5 << 8) | (((v5 >> 8) & 3) << 6) | *(_DWORD *)(*(_QWORD *)a1 + 44LL) & 0xFFFF003F;
  if ( v5 )
    v7 = v2 / (2 * v5);
  else
    v7 = v2 >> 1;
  a1[4] = v7;
  result = (unsigned int)a1[4];
  *(_DWORD *)(v6 + 44) = v9;
  return result;
}

//----- (000000006500940C) ----------------------------------------------------
unsigned __int64 __fastcall sub_6500940C(unsigned __int64 n0x65010F40, int a2)
{
  int v2; // w2
  int v3; // w1

  if ( (n0x65010F40 & 2) != 0 )
    MEMORY[0x65010F60] |= 2u;
  if ( (n0x65010F40 & 4) != 0 )
  {
    v2 = MEMORY[0x65010F60] | 4;
LABEL_7:
    MEMORY[0x65010F60] = v2;
    goto LABEL_10;
  }
  if ( (n0x65010F40 & 8) != 0 )
  {
    v2 = MEMORY[0x65010F60] | 8;
    goto LABEL_7;
  }
  if ( (n0x65010F40 & 1) != 0 )
  {
    n0x65010F40 = 0x65010F40uLL;
    MEMORY[0x65010F64] |= a2;
    v3 = MEMORY[0x65010F60] | 0x3FF;
LABEL_14:
    MEMORY[0x65010F60] = v3;
    return n0x65010F40;
  }
LABEL_10:
  if ( (n0x65010F40 & 0x100) != 0 )
    MEMORY[0x65010F60] |= 0x100u;
  if ( (n0x65010F40 & 0x200) != 0 )
  {
    n0x65010F40 = 0x65010F40uLL;
    v3 = MEMORY[0x65010F60] | 0x200;
    goto LABEL_14;
  }
  return n0x65010F40;
}

//----- (00000000650094B0) ----------------------------------------------------
__int64 __fastcall sub_650094B0(__int64 *a1)
{
  __int64 result; // x0

  result = *a1;
  *(_DWORD *)(result + 44) |= 4u;
  return result;
}

//----- (00000000650094C4) ----------------------------------------------------
unsigned __int64 sub_650094C4()
{
  return 0x65010F80uLL;
}

//----- (00000000650094D0) ----------------------------------------------------
__int64 __fastcall sub_650094D0(unsigned int a1)
{
  int v2; // w0
  int v3; // w19
  __int64 result; // x0

  v2 = sub_6500A7AC();
  v3 = v2;
  while ( 1 )
  {
    result = (unsigned int)(v2 - v3);
    if ( (unsigned int)result >= a1 )
      break;
    v2 = sub_6500A7AC();
  }
  return result;
}

//----- (0000000065009508) ----------------------------------------------------
__int64 __fastcall sub_65009508(__int64 *a1, int a2)
{
  __int64 result; // x0

  if ( a2 == 1 )
    return sub_650094B0(a1);
  result = *a1;
  if ( (*(_DWORD *)(result + 44) & 4) != 0 )
  {
    *(_DWORD *)(result + 44) &= ~4u;
    return sub_650094D0(1u);
  }
  return result;
}

//----- (0000000065009538) ----------------------------------------------------
__int64 __fastcall sub_65009538(__int64 *a1)
{
  __int64 result; // x0

  sub_65009508(a1, 0);
  result = *a1;
  *(_DWORD *)(*a1 + 44) |= 0x1000000u;
  while ( (*(_DWORD *)(result + 44) & 0x1000000) != 0 )
    ;
  return result;
}

//----- (0000000065009574) ----------------------------------------------------
__int64 __fastcall sub_65009574(__int64 n0x65010F40, unsigned int a2, int a3, __int64 a4, _BYTE *a5)
{
  __int64 v8; // x4
  char *v9; // x1
  int v10; // w6
  int v11; // w23
  int v12; // w5
  int v13; // w0
  _DWORD *v14; // x1
  int v15; // w2
  int v16; // w0
  int v17; // w24
  int v18; // w0
  unsigned int v19; // w1
  int n1703936; // w0
  unsigned __int64 v21; // x20
  unsigned int n1023; // w20
  unsigned __int64 v23; // x19
  _DWORD *v24; // x1
  __int64 n255; // x0
  __int64 n4; // x0
  int v27; // w2
  __int64 v28; // x4
  int v29; // w1
  int v30; // w0
  unsigned int v31; // w1
  unsigned int v32; // w2
  unsigned int v33; // w1
  unsigned int v34; // w2
  int v35; // w1
  int v38; // [xsp+6Ch] [xbp+6Ch]
  int n0x10000000; // [xsp+70h] [xbp+70h]
  unsigned int v40; // [xsp+74h] [xbp+74h]
  unsigned int v41; // [xsp+78h] [xbp+78h]
  unsigned int v42; // [xsp+7Ch] [xbp+7Ch]
  int v43; // [xsp+80h] [xbp+80h]
  int v44; // [xsp+84h] [xbp+84h]
  unsigned int v45; // [xsp+88h] [xbp+88h]
  unsigned int v46; // [xsp+8Ch] [xbp+8Ch]

  sub_650094B0((__int64 *)n0x65010F40);
  sub_6500928C((_QWORD *)n0x65010F40, 1023);
  v8 = *(_QWORD *)n0x65010F40;
  v38 = *(_DWORD *)(*(_QWORD *)n0x65010F40 + 52LL);
  *(_DWORD *)(v8 + 52) &= ~0x100000u;
  *(_DWORD *)(v8 + 44) = *(_DWORD *)(v8 + 44) & 0xFFF0FFFF | 0xF0000;
  *(_DWORD *)(v8 + 52) = v38;
  n0x10000000 = 0;
  v9 = (char *)&unk_6500E570 + 24 * a2;
  v10 = *((_DWORD *)v9 + 4);
  v11 = *((_DWORD *)v9 + 2);
  LOWORD(v12) = v11 | 1;
  if ( !v10 )
    v12 = *((_DWORD *)v9 + 2);
  *(_DWORD *)(n0x65010F40 + 20) = v10;
  if ( a4 )
    LOWORD(v12) = v12 | 0x40;
  if ( (v12 & 1) != 0 )
  {
    if ( (v10 & 1) != 0 )
      n0x10000000 = 0x10000000;
    if ( (v10 & 2) != 0 )
      n0x10000000 |= 0x1000000u;
    if ( (v10 & 4) != 0 )
      n0x10000000 |= 0x800000u;
    if ( (v10 & 8) != 0 )
      n0x10000000 |= 0x400000u;
    if ( (v10 & 0x10) != 0 )
      n0x10000000 |= 0x200000u;
    if ( (v10 & 0x20) != 0 )
      n0x10000000 |= 0x100000u;
    if ( (v10 & 0x40) != 0 )
      n0x10000000 |= 0x80000u;
    if ( (v10 & 0x80) != 0 )
      n0x10000000 |= 0x40000u;
    if ( (v10 & 0x100) != 0 )
      n0x10000000 |= 0x20000u;
    if ( (v10 & 0x200) != 0 )
      n0x10000000 |= 0x10000u;
  }
  if ( (v12 & 2) != 0 )
    n0x10000000 |= 0x100u;
  if ( (v12 & 4) != 0 )
    n0x10000000 |= 0x40u;
  if ( (v12 & 8) != 0 )
    n0x10000000 |= 0x80u;
  if ( (v12 & 0x10) != 0 )
    n0x10000000 |= 0x20u;
  if ( (v12 & 0x20) != 0 )
    n0x10000000 |= 0x10u;
  if ( (v12 & 0x40) != 0 )
    n0x10000000 |= 8u;
  if ( (v12 & 0x80) != 0 )
    n0x10000000 |= 4u;
  if ( (v12 & 0x100) != 0 )
    n0x10000000 |= 2u;
  if ( (v12 & 0x200) != 0 )
    n0x10000000 |= 1u;
  *(_DWORD *)(v8 + 52) |= n0x10000000;
  *(_DWORD *)(v8 + 56) |= n0x10000000;
  *(_DWORD *)(n0x65010F40 + 36) = 0;
  *(_DWORD *)(n0x65010F40 + 32) = 0;
  if ( a4 )
  {
    *(_QWORD *)(v8 + 88) = *(_QWORD *)a4;
    v13 = *(_DWORD *)(a4 + 12);
    *(_DWORD *)(v8 + 4) = *(_DWORD *)(a4 + 8);
    *(_DWORD *)v8 = v13;
  }
  *(_DWORD *)(v8 + 8) = a3;
  v14 = (_DWORD *)((char *)&unk_6500E570 + 24 * a2);
  v15 = v14[1];
  v16 = v14[5];
  v17 = v14[3];
  v40 = *(_DWORD *)(v8 + 12) & 0xC004FF80;
  if ( (v16 & 1) != 0 )
    v40 |= 0x40u;
  if ( (v16 & 2) != 0 )
    v40 |= 0x20u;
  if ( (v16 & 4) != 0 )
    v40 |= 0x10u;
  if ( (v16 & 8) != 0 )
    v40 |= 4u;
  if ( (v16 & 0x10) != 0 )
    v40 |= 2u;
  if ( (v16 & 0x20) != 0 )
    v40 |= 1u;
  if ( (v16 & 0x40) != 0 )
    v40 |= 0x200000u;
  switch ( v17 )
  {
    case 0:
      v18 = v40;
      goto LABEL_67;
    case 1:
    case 5:
    case 6:
    case 7:
      v19 = v40;
      n1703936 = 1703936;
      goto LABEL_66;
    case 2:
      v19 = v40;
      n1703936 = 589824;
      goto LABEL_66;
    case 3:
    case 4:
      v18 = v40 | 0x20000;
      goto LABEL_67;
    case 8:
    case 9:
      v19 = v40;
      n1703936 = 1769472;
LABEL_66:
      v18 = v19 | n1703936;
LABEL_67:
      v40 = v18;
      break;
    default:
      break;
  }
  *(_DWORD *)(v8 + 12) = v40 | (v15 << 24);
  while ( v11 != (*(_DWORD *)(n0x65010F40 + 32) & v11) )
  {
    v41 = *(_DWORD *)(MEMORY[0x65010F40] + 48LL);
    v21 = ((unsigned __int64)v41 >> 15) & 1;
    if ( (v41 & 0x100) != 0 )
      LODWORD(v21) = v21 | 2;
    if ( (*(_DWORD *)(MEMORY[0x65010F40] + 48LL) & 0x40) != 0 )
      LODWORD(v21) = v21 | 4;
    if ( (v41 & 0x80) != 0 )
      LODWORD(v21) = v21 | 8;
    if ( (v41 & 0x20) != 0 )
      LODWORD(v21) = v21 | 0x10;
    if ( (v41 & 0x10) != 0 )
      LODWORD(v21) = v21 | 0x20;
    if ( (v41 & 8) != 0 )
      LODWORD(v21) = v21 | 0x40;
    if ( (v41 & 4) != 0 )
      LODWORD(v21) = v21 | 0x80;
    if ( (v41 & 2) != 0 )
      LODWORD(v21) = v21 | 0x100;
    n1023 = v21 | ((*(_DWORD *)(MEMORY[0x65010F40] + 48LL) & 1) << 9);
    v42 = *(_DWORD *)(MEMORY[0x65010F40] + 48LL);
    v23 = ((unsigned __int64)v42 >> 28) & 1;
    if ( (v42 & 0x1000000) != 0 )
      LODWORD(v23) = v23 | 2;
    if ( (v42 & 0x800000) != 0 )
      LODWORD(v23) = v23 | 4;
    if ( (v42 & 0x400000) != 0 )
      LODWORD(v23) = v23 | 8;
    if ( (v42 & 0x200000) != 0 )
      LODWORD(v23) = v23 | 0x10;
    if ( (v42 & 0x100000) != 0 )
      LODWORD(v23) = v23 | 0x20;
    if ( (v42 & 0x80000) != 0 )
      LODWORD(v23) = v23 | 0x40;
    if ( (v42 & 0x40000) != 0 )
      LODWORD(v23) = v23 | 0x80;
    if ( (v42 & 0x20000) != 0 )
      LODWORD(v23) = v23 | 0x100;
    if ( (*(_DWORD *)(MEMORY[0x65010F40] + 48LL) & 0x10000) != 0 )
      LODWORD(v23) = v23 | 0x200;
    sub_6500928C((_QWORD *)0x65010F40, n1023);
    MEMORY[0x65010F58](n1023, (unsigned int)v23);
  }
  v24 = *(_DWORD **)n0x65010F40;
  *(_DWORD *)(*(_QWORD *)n0x65010F40 + 44LL) |= 0x6000000u;
  while ( (v24[11] & 0x6000000) != 0 )
    ;
  n255 = 255LL;
  if ( (*(_DWORD *)(n0x65010F40 + 32) & 1) == 0 )
  {
    v43 = v24[4];
    v44 = v24[5];
    v45 = v24[6];
    v46 = v24[7];
    n4 = 0LL;
    do
    {
      v27 = 4 * n4;
      v28 = (unsigned int)(4 * n4);
      v29 = *(&v43 + n4++);
      a5[v28] = HIBYTE(v29);
      a5[v27 + 1] = BYTE2(v29);
      a5[v27 + 2] = BYTE1(v29);
      a5[v27 + 3] = v29;
    }
    while ( n4 != 4 );
    v30 = v43;
    switch ( v17 )
    {
      case 1:
      case 3:
      case 4:
      case 5:
      case 6:
      case 7:
      case 8:
      case 9:
        *a5 = HIBYTE(v43);
        a5[3] = v30;
        a5[1] = BYTE2(v30);
        a5[2] = BYTE1(v30);
        break;
      case 2:
        v31 = v46;
        a5[2] = v46;
        *a5 = BYTE2(v31);
        v32 = v31 >> 8;
        v33 = v45;
        a5[1] = v32;
        a5[3] = HIBYTE(v33);
        a5[6] = v33;
        a5[4] = BYTE2(v33);
        v34 = v33 >> 8;
        v35 = v44;
        a5[5] = v34;
        a5[7] = HIBYTE(v35);
        a5[10] = v35;
        a5[8] = BYTE2(v35);
        a5[11] = HIBYTE(v30);
        a5[9] = BYTE1(v35);
        a5[12] = BYTE2(v30);
        a5[14] = v30;
        a5[13] = BYTE1(v30);
        break;
      default:
        return 0LL;
    }
    return 0LL;
  }
  return n255;
}
// 65010F58: using guessed type __int64 (__fastcall *)(_QWORD, _QWORD);

//----- (0000000065009C10) ----------------------------------------------------
__int64 __fastcall sub_65009C10(__int64 n0x65010F40, _BYTE *a2)
{
  int v2; // w0
  unsigned int v3; // w1
  _BYTE *v5; // [xsp+20h] [xbp+20h] BYREF
  int n512; // [xsp+28h] [xbp+28h]
  int v7; // [xsp+2Ch] [xbp+2Ch]
  _BYTE v8[512]; // [xsp+30h] [xbp+30h] BYREF

  n512 = 512;
  v5 = v8;
  v7 = 1;
  v2 = sub_65009574(n0x65010F40, 0xEu, 0, (__int64)&v5, a2);
  v3 = 0;
  if ( !v2 )
  {
    MEMORY[0x65010F78] = (v8[214] << 16) | (v8[213] << 8) | v8[212] | (v8[215] << 24);
    return 1;
  }
  return v3;
}

//----- (0000000065009C98) ----------------------------------------------------
__int64 __fastcall sub_65009C98(__int64 n0x65010F40)
{
  int v2; // w19
  _BYTE v4[16]; // [xsp+20h] [xbp+20h] BYREF

  v2 = sub_6500A7AC();
  while ( (unsigned int)sub_65009574(n0x65010F40, 8u, 0x10000, 0LL, v4) || (v4[2] & 1) == 0 )
  {
    if ( (unsigned int)sub_6500A7AC() - v2 > 0xBB8 )
      return 0LL;
  }
  return 1LL;
}

//----- (0000000065009CFC) ----------------------------------------------------
__int64 __fastcall sub_65009CFC(__int64 n0x65010F40, unsigned __int8 a2, unsigned __int8 a3)
{
  int v4; // w0
  unsigned int v5; // w1
  char v7; // [xsp+20h] [xbp+20h] BYREF

  v4 = sub_65009574(n0x65010F40, 0xBu, (a3 << 8) | 0x3000001 | (a2 << 16), 0LL, &v7);
  v5 = 0;
  if ( !v4 )
    return (unsigned int)sub_65009C98(n0x65010F40) != 0;
  return v5;
}

//----- (0000000065009D5C) ----------------------------------------------------
bool __fastcall sub_65009D5C(int n7_1, int a2, int a3, __int64 a4)
{
  int v8; // w0
  BOOL v9; // w4
  __int64 v10; // x20
  unsigned int n7; // w0
  unsigned __int8 n9; // w2
  int n5; // w20
  __int64 n0x65010F40; // x19
  _BYTE v16[16]; // [xsp+50h] [xbp+50h] BYREF
  __int64 v17; // [xsp+60h] [xbp+60h] BYREF
  int v18; // [xsp+68h] [xbp+68h]
  int v19; // [xsp+6Ch] [xbp+6Ch]
  _BYTE v20[16]; // [xsp+70h] [xbp+70h] BYREF

  if ( *(_DWORD *)(MEMORY[0x65010F38] + 52LL) == n7_1 )
    goto LABEL_13;
  v8 = sub_65009574(MEMORY[0x65010F38], 8u, 0x10000, 0LL, v16);
  v9 = 0;
  if ( !v8 )
  {
    v10 = MEMORY[0x65010F38];
    n7 = *(_DWORD *)(MEMORY[0x65010F38] + 52LL);
    if ( n7 <= 7 && n7 == n7_1 )
      goto LABEL_13;
    if ( n7_1 == 1 )
    {
      n9 = 9;
    }
    else if ( n7_1 )
    {
      if ( n7_1 != 2 )
        return 0;
      n9 = 10;
    }
    else
    {
      n9 = 8;
    }
    if ( (unsigned int)sub_65009CFC(MEMORY[0x65010F38], 0xB3u, n9) )
    {
      *(_DWORD *)(v10 + 52) = n7_1;
LABEL_13:
      n5 = 5;
      while ( 1 )
      {
        n0x65010F40 = MEMORY[0x65010F38];
        v19 = a3;
        v17 = a4;
        v18 = *(_DWORD *)(MEMORY[0x65010F38] + 48LL);
        if ( !(unsigned int)sub_65009C98(MEMORY[0x65010F38])
          || (unsigned int)sub_65009574(n0x65010F40, 9u, 512, 0LL, v20) )
        {
          goto LABEL_15;
        }
        *(_DWORD *)(n0x65010F40 + 48) = 512;
        if ( (unsigned int)sub_65009574(n0x65010F40, 0xAu, a2, (__int64)&v17, v20) )
          break;
        v9 = sub_65009574(n0x65010F40, 7u, 0, 0LL, v20) == 0;
        if ( v9 )
          return 1;
LABEL_20:
        if ( !--n5 )
          return v9;
      }
      sub_65009574(n0x65010F40, 7u, 0, 0LL, v20);
LABEL_15:
      v9 = 0;
      goto LABEL_20;
    }
    return 0;
  }
  return v9;
}

//----- (0000000065009EF0) ----------------------------------------------------
__int64 sub_65009EF0()
{
  __int64 v0; // x1
  __int64 *n0x65010F40; // x19
  __int64 v2; // x0
  unsigned int v3; // w20
  int v4; // w21
  _BYTE v6[16]; // [xsp+30h] [xbp+30h] BYREF
  char v7; // [xsp+40h] [xbp+40h] BYREF

  MEMORY[0x65010F40] = 572522496LL;
  MEMORY[0x20000000] |= 8u;
  sub_65009508((__int64 *)0x65010F40, 0);
  MEMORY[0x20000004] |= 8u;
  sub_650094D0(1u);
  MEMORY[0x20000004] &= ~8u;
  MEMORY[0x65010F48] = 1;
  MEMORY[0x65010F4C] = 384000000;
  MEMORY[0x65010F58] = (__int64 (__fastcall *)(_QWORD, _QWORD))sub_6500940C;
  MEMORY[0x65010F54] = 0;
  MEMORY[0x65010F38] = 0x65010F40uLL;
  sub_65009538((__int64 *)0x65010F40);
  sub_6500936C((_DWORD *)0x65010F40, 400000);
  MEMORY[0x20010124] = 3;
  sub_6500936C((_DWORD *)0x65010F40, 400000);
  v0 = MEMORY[0x65010F40];
  *(_DWORD *)(MEMORY[0x65010F40] + 44LL) |= 1u;
  while ( (*(_DWORD *)(v0 + 44) & 2) == 0 )
    ;
  sub_650094B0((__int64 *)0x65010F40);
  n0x65010F40 = (__int64 *)MEMORY[0x65010F38];
  *(_WORD *)(MEMORY[0x65010F38] + 40LL) = 1;
  *((_DWORD *)n0x65010F40 + 13) = 8;
  v2 = *n0x65010F40;
  *((_DWORD *)n0x65010F40 + 12) = 0;
  *((_DWORD *)n0x65010F40 + 11) = 0;
  *(_DWORD *)(v2 + 60) |= 0x20000000u;
  *(_DWORD *)(v2 + 40) &= 0xFFFFFFE7;
  if ( (unsigned int)sub_65009574((__int64)n0x65010F40, 0, 0, 0LL, v6) )
    goto LABEL_4;
  v4 = sub_6500A7AC();
  while ( 1 )
  {
    while ( (unsigned int)sub_65009574((__int64)n0x65010F40, 1u, 1090486272, 0LL, v6) )
      ;
    if ( (v6[0] & 0x80000000) != 0 )
      break;
    if ( (unsigned int)sub_6500A7AC() - v4 > 0x2710 )
      goto LABEL_4;
  }
  if ( (unsigned int)sub_65009574((__int64)n0x65010F40, 2u, 0, 0LL, v6)
    || (unsigned int)sub_65009574((__int64)n0x65010F40, 3u, 0x10000, 0LL, v6)
    || (v3 = sub_65009574((__int64)n0x65010F40, 5u, 0x10000, 0LL, v6)) != 0
    || !(unsigned int)sub_65009C10((__int64)n0x65010F40, v6)
    || !(unsigned int)sub_65009CFC((__int64)n0x65010F40, 0xB9u, 1u)
    || (sub_65009508(n0x65010F40, 0),
        sub_6500936C(n0x65010F40, 48000000),
        sub_650094B0(n0x65010F40),
        !(unsigned int)sub_65009CFC((__int64)n0x65010F40, 0xB7u, 2u)) )
  {
LABEL_4:
    v3 = 0;
  }
  else
  {
    *(_DWORD *)(*n0x65010F40 + 40) = *(_DWORD *)(*n0x65010F40 + 40) & 0xFFFFFFDD | 0x20;
    *((_DWORD *)n0x65010F40 + 11) = 2;
    if ( !(unsigned int)sub_65009574((__int64)n0x65010F40, 9u, 512, 0LL, &v7) )
    {
      v3 = 1;
      *((_DWORD *)n0x65010F40 + 12) = 512;
    }
  }
  MEMORY[0x65010F88] = 5;
  MEMORY[0x65010F98] = 512LL;
  MEMORY[0x65010FF0] = sub_65009D5C;
  MEMORY[0x65010F84] = 0;
  MEMORY[0x65010F90] = MEMORY[0x65010F78];
  return v3;
}
// 65010F58: using guessed type __int64 (__fastcall *)(_QWORD, _QWORD);

//----- (000000006500A1E8) ----------------------------------------------------
__int64 sub_6500A1E8()
{
  return sub_65009508(MEMORY[0x65010F38], 0);
}

//----- (000000006500A200) ----------------------------------------------------
char *__fastcall sub_6500A200(__int64 a1)
{
  __int64 n18_1; // x19
  __int64 n18; // x20

  n18_1 = 0LL;
  while ( 1 )
  {
    n18 = (int)n18_1;
    if ( !(unsigned int)sub_65002078(qword_6500FF10[5 * n18_1], a1) )
      break;
    if ( ++n18_1 == 18 )
    {
      n18 = 18LL;
      break;
    }
  }
  if ( (unsigned __int8)n18 == 18 )
    return 0LL;
  else
    return (char *)&qword_6500FF10[5 * n18];
}
// 6500FF10: using guessed type _QWORD qword_6500FF10[30];

//----- (000000006500A288) ----------------------------------------------------
__int64 __fastcall sub_6500A288(__int64 a1, int a2)
{
  char *v2; // x19
  int v3; // w0
  int i; // w21
  unsigned int v5; // w20
  bool v6; // zf
  int v7; // w0
  __int16 v8; // w0

  v2 = sub_6500A200(a1);
  v3 = *((_DWORD *)v2 + 8);
  for ( i = 0; i != 32; ++i )
  {
    if ( (v3 & 1) != 0 )
      break;
    v3 >>= 1;
  }
  v5 = (*((unsigned __int16 *)v2 + 12) + 1000 * (a2 - *((unsigned __int16 *)v2 + 10)) - 1)
     / *((unsigned __int16 *)v2 + 12);
  v6 = (unsigned int)sub_65002078(*(_QWORD *)v2, (__int64)"vddbst") == 0;
  v7 = *((_DWORD *)v2 + 7);
  if ( v6 )
    v5 += 177;
  if ( !v7 || (v5 & 0x80000000) != 0 )
    return 0xFFFFFFFFLL;
  if ( v5 > *((_DWORD *)v2 + 8) >> i )
    v5 = *((_DWORD *)v2 + 8) >> i;
  v8 = sub_65008940(v7);
  sub_6500A1FC(
    *((unsigned int *)v2 + 7),
    *((unsigned __int16 *)v2 + 16) & (v5 << i) | (unsigned __int16)(v8 & ~*((_WORD *)v2 + 16)));
  return 0LL;
}
// 6500A1FC: using guessed type __int64 __fastcall sub_6500A1FC(_QWORD, _QWORD);

//----- (000000006500A364) ----------------------------------------------------
__int64 sub_6500A364()
{
  sub_6500A1FC(1682056024LL, 17LL);
  sub_6500A1FC(1682056028LL, 95LL);
  sub_6500A1FC(1682056144LL, 28287LL);
  sub_6500A1FC(1682056148LL, 8215LL);
  sub_6500897C(0x644221ACu, 1, 0);
  sub_6500897C(0x644221B8u, 1, 0);
  sub_6500A1FC(1682088196LL, 127LL);
  sub_6500A1FC(1682088244LL, 28287LL);
  sub_6500A1FC(1682088248LL, 8215LL);
  return 0LL;
}
// 6500A1FC: using guessed type __int64 __fastcall sub_6500A1FC(_QWORD, _QWORD);

//----- (000000006500A410) ----------------------------------------------------
__int64 __fastcall sub_6500A410(__int64 a1)
{
  char *v1; // x19
  int v2; // w0
  int v3; // w0
  int v4; // w2
  int n32; // w1
  unsigned int v6; // w0

  v1 = sub_6500A200(a1);
  v2 = *((_DWORD *)v1 + 7);
  if ( !v2 )
    return 0xFFFFFFFFLL;
  v3 = sub_65008940(v2);
  v4 = *((_DWORD *)v1 + 8);
  n32 = 0;
  v6 = v3 & v4;
  do
  {
    if ( (v4 & 1) != 0 )
      break;
    ++n32;
    v4 >>= 1;
  }
  while ( n32 != 32 );
  return (v6 >> n32) * *((_DWORD *)v1 + 6) / 0x3E8 + *((_DWORD *)v1 + 5);
}

//----- (000000006500A484) ----------------------------------------------------
__int64 __fastcall sub_6500A484(__int64 a1, int a2)
{
  int n2; // w0
  int v5; // w19

  n2 = *((_WORD *)sub_6500A200(a1) + 4) & 0xF;
  if ( n2 == 2 )
  {
    v5 = sub_6500A410(a1);
    if ( v5 >= a2 )
    {
      do
      {
        v5 -= 25;
        if ( a2 >= v5 )
          v5 = a2;
        sub_6500A288(a1, v5);
      }
      while ( v5 > a2 );
    }
    else
    {
      do
      {
        v5 += 25;
        if ( a2 <= v5 )
          v5 = a2;
        sub_6500A288(a1, v5);
      }
      while ( v5 < a2 );
    }
  }
  else if ( !n2 )
  {
    return sub_6500A288(a1, a2);
  }
  return 0LL;
}

//----- (000000006500A53C) ----------------------------------------------------
__int64 __fastcall sub_6500A53C(int a1)
{
  int n29; // w20
  int n28; // w0
  _DWORD *v3; // x19
  __int64 result; // x0
  int v5; // w1
  int v6; // [xsp+28h] [xbp+28h] BYREF
  int v7; // [xsp+2Ch] [xbp+2Ch] BYREF

  v6 = 0;
  v7 = 0;
  if ( a1 )
  {
    n29 = 29;
    n28 = 28;
    v3 = &unk_650101E0;
  }
  else
  {
    n29 = 32;
    n28 = 33;
    v3 = &unk_650101F0;
  }
  result = sub_6500A664(n28, &v6);
  if ( !(_DWORD)result )
  {
    result = sub_6500A664(n29, &v7);
    if ( !(_DWORD)result )
    {
      v5 = (unsigned __int16)v7 >> 4;
      if ( (unsigned __int16)v7 >> 4 && (unsigned __int16)v6 >> 4 )
      {
        v3[1] = (unsigned __int16)v6 >> 4;
        v3[3] = v5;
      }
      else
      {
        return 0LL;
      }
    }
  }
  return result;
}

//----- (000000006500A5C8) ----------------------------------------------------
__int64 sub_6500A5C8()
{
  __int64 result; // x0

  sub_6500897C(0x64422008u, 32, 0);
  sub_6500897C(0x6442200Cu, 96, 0);
  sub_6500897C(0x64422378u, 256, 0);
  sub_6500897C(0x64420504u, 1, 0);
  sub_6500897C(0x64420504u, 4096, 0);
  sub_6500897C(0x64420504u, 1280, 1792);
  result = sub_6500A53C(1);
  if ( !(_DWORD)result )
    return sub_6500A53C(0);
  return result;
}

//----- (000000006500A664) ----------------------------------------------------
__int64 __fastcall sub_6500A664(int a1, _DWORD *a2)
{
  sub_6500897C(0x64422008u, 64, 0);
  sub_6500897C(0x64422010u, 2048, 0);
  sub_6500897C(0x64422000u, 0, 8);
  sub_6500897C(0x64420C10u, 4, 0);
  *a2 = sub_65008940(4 * (a1 + 420512528));
  return 0LL;
}

//----- (000000006500A6F0) ----------------------------------------------------
__int64 __fastcall sub_6500A6F0(_BYTE *i_1)
{
  _BYTE *i; // x1

  for ( i = i_1; *i; ++i )
    ;
  return (unsigned int)((_DWORD)i - (_DWORD)i_1);
}

//----- (000000006500A70C) ----------------------------------------------------
__int64 __fastcall sub_6500A70C(int a1)
{
  __int64 n1677983824; // x0

  MEMORY[0x64040040] = 26 * a1;
  MEMORY[0x64040050] = 2;
  while ( MEMORY[0x64040058] )
    ;
  n1677983824 = 1677983824LL;
  MEMORY[0x64040050] &= ~2u;
  return n1677983824;
}

//----- (000000006500A760) ----------------------------------------------------
__int64 __fastcall sub_6500A760(int a1)
{
  __int64 n1677983760; // x0

  MEMORY[0x64040000] = 32 * a1;
  MEMORY[0x64040010] = 2;
  while ( MEMORY[0x64040018] )
    ;
  n1677983760 = 1677983760LL;
  MEMORY[0x64040010] &= ~2u;
  return n1677983760;
}

//----- (000000006500A7AC) ----------------------------------------------------
__int64 sub_6500A7AC()
{
  __int64 i_1; // x0
  unsigned int i; // w1

  i_1 = MEMORY[0x641C000C];
  for ( i = MEMORY[0x641C000C]; (_DWORD)i_1 != i; i = MEMORY[0x641C000C] )
    i_1 = i;
  return i_1;
}

//----- (000000006500A840) ----------------------------------------------------
__int64 __fastcall sub_6500A840(int *a1)
{
  int v2; // w1
  __int16 v3; // w2
  int v4; // w21
  int n0x100000; // w20
  int v6; // w1
  __int16 n2; // w2

  sub_6500895C(0x64420060u, 0xE551u);
  v2 = a1[1];
  if ( v2 )
  {
    if ( v2 != 1 )
      goto LABEL_6;
    v3 = 0;
  }
  else
  {
    v3 = 1;
  }
  sub_6500897C(0x64420048u, v2, v3);
LABEL_6:
  if ( *a1 )
  {
    v4 = a1[2];
    n0x100000 = 0x100000;
    do
    {
      if ( (sub_65008940(1682047056) & 0x10) == 0 )
        break;
      --n0x100000;
    }
    while ( n0x100000 );
    sub_6500895C(0x64420044u, HIWORD(v4));
    sub_6500895C(0x64420040u, v4);
  }
  v6 = *a1;
  if ( !*a1 )
  {
    n2 = 2;
    goto LABEL_15;
  }
  if ( v6 == 1 )
  {
    LOWORD(v6) = 10;
    n2 = 0;
LABEL_15:
    sub_6500897C(0x64420048u, v6, n2);
  }
  sub_6500895C(0x64420060u, 0x1AAEu);
  return 0LL;
}

//----- (000000006500A934) ----------------------------------------------------
__int64 __fastcall sub_6500A934(unsigned int n0x50)
{
  unsigned int n80; // w0
  int v4[4]; // [xsp+20h] [xbp+20h] BYREF

  sub_6500897C(0x64422008u, 4, 0);
  sub_6500897C(0x64422010u, 4, 0);
  v4[0] = 1;
  n80 = 80;
  v4[1] = 0;
  if ( n0x50 > 0x50 )
    n80 = ((n0x50 % 0x3E8) << 15) / 0x3E8 + ((n0x50 / 0x3E8) << 15);
  v4[2] = n80;
  return sub_6500A840(v4);
}

//----- (000000006500AA0C) ----------------------------------------------------
__int64 __fastcall sub_6500AA0C(__int64 a1, __int64 a2, unsigned int a3, unsigned int a4, __int64 a5)
{
  unsigned __int64 v6; // x2
  unsigned __int64 v7; // x20
  unsigned __int64 i; // x23
  unsigned int v11; // w21
  __int64 v13; // [xsp+40h] [xbp+40h] BYREF
  _BYTE v14[512]; // [xsp+A0h] [xbp+A0h] BYREF

  v6 = *(_QWORD *)(a1 + 24);
  v7 = a4 / v6;
  i = a4 % v6;
  if ( a5 )
  {
    v11 = sub_65001BE0(a1, a2, &v13);
    if ( !v11
      && (!v7
       || (*(__int64 (__fastcall **)(_QWORD, __int64, unsigned __int64, __int64))(a1 + 112))(
            *(unsigned int *)(a1 + 4),
            v13 + a3,
            v7,
            a5)) )
    {
      if ( !(_DWORD)i )
        return v11;
      if ( (*(__int64 (__fastcall **)(_QWORD, unsigned __int64, __int64, _BYTE *))(a1 + 112))(
             *(unsigned int *)(a1 + 4),
             v13 + a3 + v7,
             1LL,
             v14) == 1 )
      {
        sub_6500201C(a5 + v7 * *(_QWORD *)(a1 + 24), (__int64)v14, (unsigned int)i);
        return v11;
      }
    }
  }
  return (unsigned int)-1;
}

//----- (000000006500AAE4) ----------------------------------------------------
__int64 __fastcall sub_6500AAE4(__int64 a1, unsigned int a2, __int64 a3)
{
  int n5; // w19
  unsigned __int64 v7; // x20
  __int64 result; // x0

  n5 = 5;
  v7 = sub_65001BDC();
  if ( !v7 )
    return 0xFFFFFFFFLL;
  while ( 1 )
  {
    result = sub_6500AA0C(v7, a1, 0, a2, a3);
    if ( !(_DWORD)result )
      break;
    if ( !--n5 )
      return 0xFFFFFFFFLL;
  }
  return result;
}

//----- (000000006500AB54) ----------------------------------------------------
__int64 __fastcall sub_6500AB54(__int64 a1, unsigned int n0x200_1, __int64 a3, __int64 a4)
{
  int n5; // w20
  unsigned __int64 v9; // x19
  __int64 result; // x0
  unsigned int n0x200; // w22
  int n5_1; // w20

  n5 = 5;
  v9 = sub_65001BDC();
  if ( !v9 )
    return 0xFFFFFFFFLL;
  while ( (unsigned int)sub_6500AA0C(v9, a1, 0, 0x200u, a4) )
  {
    if ( !--n5 )
      return 0xFFFFFFFFLL;
  }
  n0x200 = *(_DWORD *)(a4 + 48) + 1256;
  if ( n0x200 > n0x200_1 )
    return 4294967294LL;
  n5_1 = 5;
  while ( 1 )
  {
    result = sub_6500AA0C(v9, a1, 0x200uLL / *(_QWORD *)(v9 + 24), n0x200, a3);
    if ( !(_DWORD)result )
      break;
    if ( !--n5_1 )
      return 4294967293LL;
  }
  return result;
}

//----- (000000006500AC18) ----------------------------------------------------
bool sub_6500AC18()
{
  sub_6500897C(0x64422008u, 4, 0);
  sub_6500897C(0x64422010u, 4, 0);
  sub_65008940(1682047056);
  return (unsigned __int8)sub_65008940(1682056108) == 2;
}

//----- (000000006500AC74) ----------------------------------------------------
__int64 __fastcall sub_6500AC74(int a1)
{
  __int64 n2; // x0
  unsigned __int16 n21964; // w1
  __int64 v4; // x0

  nullsub_1(8LL);
  n2 = (unsigned __int8)sub_65001FE8();
  if ( (_BYTE)n2 )
  {
    n21964 = 21964;
    if ( (unsigned __int8)n2 != 1 )
    {
      if ( (unsigned __int8)n2 != 2 )
      {
LABEL_6:
        sub_650021A0("\r\n adjust spl\n");
        v4 = nullsub_1(31LL);
        nullsub_2(v4);
        sub_6500AB54((__int64)&aUbootA[16 * a1], 0x300000u, 3036676096LL, 3036675584LL);
        sub_65000AE4(260LL);
        return MEMORY[0xB5000000](0LL, 0LL);
      }
      n21964 = 21981;
    }
    MEMORY[0x8200003C] = n21964 | (n21964 << 16);
    goto LABEL_6;
  }
  return n2;
}
// 65001FF4: using guessed type __int64 __fastcall nullsub_1(_QWORD);
// 65001FFC: using guessed type __int64 __fastcall nullsub_2(_QWORD);

//----- (000000006500AD18) ----------------------------------------------------
__int64 sub_6500AD18()
{
  bool v0; // w0
  BOOL v1; // w19
  int v2; // w0
  __int64 v3; // x20
  __int64 v4; // x20
  __int64 v5; // x0
  __int64 v6; // x0
  __int64 v8; // x0
  __int64 v9; // x1
  char v10; // [xsp+37h] [xbp+37h] BYREF
  __int64 a3; // [xsp+38h] [xbp+38h] BYREF
  __int64 v12; // [xsp+40h] [xbp+40h] BYREF
  _QWORD v13[3]; // [xsp+48h] [xbp+48h] BYREF
  _DWORD v14[2]; // [xsp+60h] [xbp+60h] BYREF
  __int64 v15; // [xsp+68h] [xbp+68h] BYREF

  a3 = 3170893824LL;
  v12 = 3171287040LL;
  v13[1] = 3171155968LL;
  v14[1] = 0x20000;
  v10 = 0;
  nullsub_1(4LL);
  nullsub_1(5LL);
  v0 = sub_6500AC18();
  v1 = v0;
  if ( !v0 )
    sub_6500D868();
  nullsub_1(6LL);
  if ( (unsigned int)sub_65009EF0() == 1 )
  {
    nullsub_1(7LL);
    v2 = sub_65001E28(&v10);
    v3 = v2;
    if ( v2 < 0 )
      sub_65001FF8(17LL);
    sub_6500AC74(v2);
    if ( !v1 )
    {
      nullsub_1(9LL);
      sub_6500AB54((__int64)&aTeecfgA[16 * v3], 0x100000u, 3171155968LL, 3171155456LL);
      if ( sub_6500B030(1694500864LL, 3171155456LL) )
        sub_65001FF8(18LL);
      nullsub_1(10LL);
      if ( (sub_6500D77C((__int64)"tee_sml_addr", (__int64)&a3) & 0x80000000) != 0
        || (sub_6500D77C((__int64)"tee_tos_addr", (__int64)&v12) & 0x80000000) != 0 )
      {
        sub_650021A0("get sml/tos addr error!\n");
        sub_65001FF8(20LL);
      }
      v13[0] = a3;
      v13[2] = v12;
      sub_6500AB54((__int64)&aSmlA[16 * v3], 0x100000u, a3, a3 - 512);
      sub_6500AC14((__int64)&aTrustosA[16 * v3], 0x600000u, v12, v12 - 512);
    }
    nullsub_1(11LL);
    sub_6500AB54((__int64)&aUbootA[16 * v3], 0x300000u, 3036676096LL, 3036675584LL);
  }
  nullsub_1(12LL);
  if ( !v1 )
  {
    if ( (sub_6500D77C((__int64)"tee_sml_size", (__int64)v14) & 0x80000000) != 0
      || (sub_6500D77C((__int64)"tee_tos_size", (__int64)&v15) & 0x80000000) != 0 )
    {
      sub_650021A0("get sml/tos size error!\n");
      sub_65001FF8(26LL);
    }
    sub_6500DB1C((__int64)v13);
    nullsub_1(13LL);
    if ( sub_6500B030(1694500864LL, a3 - 512) )
      sub_65001FF8(27LL);
    nullsub_1(14LL);
    if ( sub_6500B030(1694500864LL, v12 - 512) )
      sub_65001FF8(28LL);
  }
  nullsub_1(15LL);
  if ( sub_6500B030(1694500864LL, 3036675584LL) )
    sub_65001FF8(30LL);
  if ( v10 )
    sub_6500B038();
  v4 = 3036676096LL;
  if ( !v1 )
    v4 = a3;
  v5 = nullsub_4();
  nullsub_5(v5);
  sub_6500A1E8();
  if ( !v1 )
    sub_6500DB40();
  sub_65000AE4(260LL);
  v6 = nullsub_1(32LL);
  nullsub_2(v6);
  if ( v4 == a3 )
    return ((__int64 (__fastcall *)(__int64, __int64, _QWORD))v4)(v12, 3171155968LL, 0LL);
  v8 = ((__int64 (__fastcall *)(_QWORD, _QWORD))v4)(0LL, 0LL);
  return sub_6500B030(v8, v9);
}
// 6500B02D: variable 'v9' is possibly undefined
// 65001FF4: using guessed type __int64 __fastcall nullsub_1(_QWORD);
// 65001FF8: using guessed type void __fastcall __noreturn sub_65001FF8(_QWORD);
// 65001FFC: using guessed type __int64 __fastcall nullsub_2(_QWORD);
// 6500B038: using guessed type __int64 sub_6500B038(void);
// 6500D774: using guessed type __int64 nullsub_4(void);
// 6500D778: using guessed type __int64 __fastcall nullsub_5(_QWORD);

//----- (000000006500B030) ----------------------------------------------------
bool __fastcall sub_6500B030(__int64 a1, __int64 a2)
{
  return sub_6500C154(a1, a2);
}

//----- (000000006500B03C) ----------------------------------------------------
bool __fastcall sub_6500B03C(__int64 a1, unsigned int a2)
{
  return ((1 << (a2 & 0x1F)) & *(_DWORD *)(a1 + 4LL * (a2 >> 5))) != 0;
}

//----- (000000006500B05C) ----------------------------------------------------
bool __fastcall sub_6500B05C(__int64 a1)
{
  return sub_6500B594(0, 7u, a1, 0);
}

//----- (000000006500B070) ----------------------------------------------------
__int64 __fastcall sub_6500B070(int a1, __int64 a2, int *a3, int *a4)
{
  int n15; // w0

  switch ( a1 )
  {
    case 21:
      n15 = 15;
LABEL_7:
      *a4 = n15;
      *a3 = n15;
      return 0LL;
    case 5:
      n15 = 3840;
      goto LABEL_7;
    case 39:
      n15 = 983040;
      goto LABEL_7;
  }
  return 0LL;
}

//----- (000000006500B0AC) ----------------------------------------------------
__int64 __fastcall sub_6500B0AC(unsigned int n0xFE, _DWORD *a2)
{
  __int64 n3; // x0
  __int64 n32; // x21
  int v6; // w22
  __int64 v7; // x0
  __int64 n32_1; // x21
  int v9; // w22
  __int64 v10; // x0
  int v11; // w21
  __int64 i; // x20
  __int64 v13; // x0
  __int64 v14; // [xsp+40h] [xbp+40h] BYREF
  __int64 v15; // [xsp+48h] [xbp+48h]
  __int64 v16; // [xsp+50h] [xbp+50h]
  __int64 v17; // [xsp+58h] [xbp+58h]

  if ( n0xFE - 13 <= 7 )
    return 0LL;
  n3 = 3LL;
  if ( n0xFE > 0xFE )
    return n3;
  if ( n0xFE - 47 <= 1 )
  {
    v14 = 0LL;
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    sub_6500B05C((__int64)&v14);
    if ( !(HIDWORD(v14) | (unsigned int)v14 | (unsigned int)v15 | HIDWORD(v15) | (unsigned int)v16 | HIDWORD(v16) | (unsigned int)v17 | HIDWORD(v17)) )
      *a2 = 0;
    v14 = 0LL;
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    sub_6500B05C((__int64)&v14);
    n32 = 0LL;
    v6 = 1;
    do
    {
      v7 = n32 + 1694555576;
      n32 += 4LL;
      v6 &= sub_6500B03C((__int64)&v14, 2 * *(_DWORD *)(v7 + 84));
    }
    while ( n32 != 32 );
    if ( v6 )
      *a2 = 15;
    v14 = 0LL;
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    sub_6500B05C((__int64)&v14);
    n32_1 = 0LL;
    v9 = 1;
    do
    {
      v10 = n32_1 + 1694555576;
      n32_1 += 4LL;
      v9 &= sub_6500B03C((__int64)&v14, 2 * *(_DWORD *)(v10 + 20));
    }
    while ( n32_1 != 32 );
    if ( v9 )
      *a2 |= 0xF00u;
    v14 = 0LL;
    v15 = 0LL;
    v16 = 0LL;
    v17 = 0LL;
    v11 = 1;
    sub_6500B05C((__int64)&v14);
    for ( i = 0LL; i != 32; i += 4LL )
    {
      v13 = i + 1694555576;
      v11 &= sub_6500B03C((__int64)&v14, 2 * *(_DWORD *)(v13 + 156));
    }
    if ( v11 )
      *a2 |= 0xF0000u;
    return 0LL;
  }
  return sub_6500B594(
           *(_DWORD *)&byte_6500DDB0[4 * n0xFE + 8],
           *(_DWORD *)&byte_6500DDB0[4 * n0xFE + 8],
           (__int64)a2,
           1);
}
// 6500DDB0: using guessed type _BYTE byte_6500DDB0[200];

//----- (000000006500B274) ----------------------------------------------------
__int64 __fastcall sub_6500B274(unsigned int n3_1, int a2)
{
  __int64 n3; // x0
  unsigned int i; // w0
  int v5; // [xsp+1Ch] [xbp+1Ch] BYREF

  v5 = a2;
  if ( n3_1 - 47 <= 1 )
    return 0LL;
  n3 = 3LL;
  if ( n3_1 <= 0xFE )
  {
    i = *(_DWORD *)&byte_6500DDB0[4 * n3_1 + 8];
    if ( n3_1 == 3 || n3_1 - 37 <= 1 )
      return sub_6500B6B4(i, v5);
    else
      return sub_6500B394(i, i, (__int64)&v5, 1);
  }
  return n3;
}
// 6500DDB0: using guessed type _BYTE byte_6500DDB0[200];

//----- (000000006500B2F0) ----------------------------------------------------
__int64 sub_6500B2F0()
{
  __int64 n1679163456; // x0

  MEMORY[0x6482001C] |= 1u;
  MEMORY[0x64160048] = 0xFFFF;
  MEMORY[0x64160040] |= 1u;
  n1679163456 = 1679163456LL;
  MEMORY[0x64160040] |= 4u;
  return n1679163456;
}

//----- (000000006500B358) ----------------------------------------------------
__int64 sub_6500B358()
{
  __int64 n1686241308; // x0

  MEMORY[0x64160040] &= ~1u;
  n1686241308 = 1686241308LL;
  MEMORY[0x6482001C] &= ~1u;
  return n1686241308;
}

//----- (000000006500B394) ----------------------------------------------------
__int64 __fastcall sub_6500B394(unsigned int i_1, unsigned int i_2, __int64 a3, int a4)
{
  unsigned int v5; // w3
  unsigned int i; // w3
  int v7; // w4
  unsigned int v8; // w0
  unsigned int n2; // w19

  sub_6500B2F0();
  MEMORY[0x6416004C] = 34832;
  MEMORY[0x64160048] = 0xFFFF;
  MEMORY[0x64160040] |= 0x10u;
  MEMORY[0x64160054] |= 4u;
  MEMORY[0x64160054] &= ~2u;
  sub_6500A760(1);
  MEMORY[0x64160054] |= 1u;
  sub_6500A760(1);
  MEMORY[0x64160040] |= 2u;
  if ( a4 )
    v5 = MEMORY[0x64160040] | 4;
  else
    v5 = MEMORY[0x64160040] & 0xFFFFFFFB;
  MEMORY[0x64160040] = v5;
  for ( i = i_1; i <= i_2; ++i )
  {
    v7 = *(_DWORD *)(a3 + 4LL * (i - i_1));
    v8 = 4 * i + 1679167488;
    *(_DWORD *)v8 = v7;
  }
  if ( MEMORY[0x64160044] )
    n2 = 2;
  else
    n2 = 0;
  MEMORY[0x64160054] &= ~1u;
  sub_6500A760(1);
  MEMORY[0x64160054] |= 2u;
  sub_6500A760(1);
  MEMORY[0x64160054] &= ~4u;
  MEMORY[0x64160040] &= ~0x10u;
  MEMORY[0x6416004C] = 0;
  sub_6500B358();
  return n2;
}

//----- (000000006500B594) ----------------------------------------------------
bool __fastcall sub_6500B594(unsigned int i_1, unsigned int i_2, __int64 a3, int a4)
{
  unsigned int v4; // w4
  unsigned int i; // w3
  unsigned int v6; // w5
  unsigned int v7; // w4
  int v8; // w19

  sub_6500B2F0();
  MEMORY[0x6416004C] = 34832;
  MEMORY[0x64160048] = 0xFFFF;
  MEMORY[0x64160040] |= 1u;
  if ( a4 )
    v4 = MEMORY[0x64160040] | 4;
  else
    v4 = MEMORY[0x64160040] & 0xFFFFFFFB;
  MEMORY[0x64160040] = v4;
  for ( i = i_1; ; ++i )
  {
    v6 = i - i_1;
    if ( i > i_2 )
      break;
    v7 = 4 * i + 1679167488;
    *(_DWORD *)(a3 + 4LL * v6) = *(_DWORD *)v7;
  }
  v8 = MEMORY[0x64160044];
  MEMORY[0x64160040] &= ~1u;
  MEMORY[0x6416004C] = 0;
  sub_6500B358();
  return v8 != 0;
}

//----- (000000006500B6B4) ----------------------------------------------------
__int64 __fastcall sub_6500B6B4(int a1, int a2)
{
  unsigned int n2; // w20

  sub_6500B2F0();
  MEMORY[0x6416004C] = 34832;
  MEMORY[0x64160048] = 0xFFFF;
  MEMORY[0x64160054] |= 4u;
  MEMORY[0x64160054] &= ~2u;
  sub_6500A760(1);
  MEMORY[0x64160054] |= 1u;
  sub_6500A760(1);
  MEMORY[0x64160040] &= ~2u;
  MEMORY[0x64160040] |= 4u;
  *(_DWORD *)(unsigned int)(4 * a1 + 1679167488) = a2;
  if ( MEMORY[0x64160044] )
    n2 = 2;
  else
    n2 = 0;
  MEMORY[0x64160054] &= ~1u;
  sub_6500A760(1);
  MEMORY[0x64160054] |= 2u;
  sub_6500A760(1);
  MEMORY[0x64160054] &= ~4u;
  MEMORY[0x6416004C] = 0;
  sub_6500B358();
  return n2;
}

//----- (000000006500B840) ----------------------------------------------------
__int64 __fastcall sub_6500B840(unsigned int n0xFE, int a2)
{
  __int64 result; // x0
  int v5; // [xsp+2Ch] [xbp+2Ch] BYREF

  v5 = 0;
  result = sub_6500B0AC(n0xFE, &v5);
  if ( !(_DWORD)result && (a2 & ~v5) != 0 )
  {
    if ( (unsigned int)sub_6500B274(n0xFE, a2 & (unsigned int)~v5) )
      return 4294901762LL;
    else
      return 0LL;
  }
  return result;
}

//----- (000000006500B890) ----------------------------------------------------
__int64 __fastcall sub_6500B890(unsigned int n0xFE, int a2)
{
  unsigned int v4; // w2
  int v5; // w0
  int v7; // [xsp+28h] [xbp+28h] BYREF
  int v8; // [xsp+2Ch] [xbp+2Ch] BYREF

  v7 = 0;
  v8 = 0;
  if ( (unsigned int)sub_6500B0AC(n0xFE, &v8) )
    return (unsigned int)-65533;
  v5 = sub_6500B840(n0xFE, a2);
  v4 = -65534;
  if ( v5 )
    return v4;
  v4 = sub_6500B0AC(n0xFE, &v7);
  if ( v4 )
  {
    return (unsigned int)-65533;
  }
  else if ( (a2 | v8) != v7 )
  {
    return (unsigned int)-65532;
  }
  return v4;
}

//----- (000000006500B910) ----------------------------------------------------
__int64 __fastcall sub_6500B910(int a1, unsigned __int64 *a2)
{
  __int64 result; // x0
  unsigned int v4; // [xsp+28h] [xbp+28h] BYREF
  unsigned int v5; // [xsp+2Ch] [xbp+2Ch] BYREF

  v4 = 0;
  v5 = 0;
  if ( !a2 || a1 != 1 )
    return 4294901761LL;
  sub_6500B0AC(0x26u, &v4);
  result = sub_6500B0AC(0x25u, &v5);
  *a2 = v4 | ((unsigned __int64)v5 << 32);
  return result;
}

//----- (000000006500B970) ----------------------------------------------------
__int64 __fastcall sub_6500B970(_DWORD *a1)
{
  __int64 result; // x0
  int v3; // [xsp+2Ch] [xbp+2Ch] BYREF
  int v4; // [xsp+30h] [xbp+30h] BYREF
  int v5; // [xsp+34h] [xbp+34h] BYREF
  int v6; // [xsp+38h] [xbp+38h] BYREF
  int v7; // [xsp+3Ch] [xbp+3Ch] BYREF
  int v8; // [xsp+40h] [xbp+40h] BYREF
  int v9; // [xsp+44h] [xbp+44h] BYREF
  int v10; // [xsp+48h] [xbp+48h] BYREF
  int v11; // [xsp+4Ch] [xbp+4Ch] BYREF

  v3 = 0;
  v4 = 0;
  v5 = 0;
  v6 = 0;
  v7 = 0;
  v8 = 0;
  v9 = 0;
  v10 = 0;
  result = 4294901761LL;
  if ( a1 )
  {
    result = sub_6500B0AC(0x2Fu, &v3);
    if ( !(_DWORD)result )
    {
      if ( !v3 )
        *a1 = 0;
      sub_6500B070(21, 28LL, &v5, &v6);
      sub_6500B070(5, 12LL, &v7, &v8);
      sub_6500B070(39, 46LL, &v9, &v10);
      if ( (v5 & v3) == v5 )
        *a1 = 1;
      if ( !v6 && !v8 && !v10 || (result = sub_6500B0AC(0x30u, &v4), !(_DWORD)result) )
      {
        if ( ((v5 | v7) & v3) == (v5 | v7) && ((v8 | v6) & v4) == (v8 | v6)
          || ((v5 | v9) & v3) == (v5 | v9) && ((v10 | v6) & v4) == (v10 | v6) )
        {
          *a1 = 5;
        }
        v11 = 0;
        return sub_6500B0AC(3u, &v11);
      }
    }
  }
  return result;
}

//----- (000000006500BAD8) ----------------------------------------------------
__int64 __fastcall sub_6500BAD8(int a1, __int64 a2)
{
  __int64 result; // x0
  int n5; // [xsp+2Ch] [xbp+2Ch] BYREF

  n5 = 0;
  result = 4294901761LL;
  if ( a2 )
  {
    result = sub_6500B970(&n5);
    if ( n5 == 5 )
    {
      result = 4294901761LL;
      if ( a1 == 1 )
      {
        sub_6500B890(0x26u, a2);
        return sub_6500B890(0x25u, SHIDWORD(a2));
      }
    }
  }
  return result;
}

//----- (000000006500BB3C) ----------------------------------------------------
__int64 __fastcall sub_6500BB3C(__int64 result, char a2, int i_1)
{
  __int64 i; // x3

  for ( i = 0LL; i_1 != (_DWORD)i; ++i )
    *(_BYTE *)(result + i) = a2;
  return result;
}

//----- (000000006500BB58) ----------------------------------------------------
__int64 __fastcall sub_6500BB58(__int64 result, __int64 a2, int i_1)
{
  __int64 i; // x3

  for ( i = 0LL; i_1 != (_DWORD)i; ++i )
    *(_BYTE *)(result + i) = *(_BYTE *)(a2 + i);
  return result;
}

//----- (000000006500BB78) ----------------------------------------------------
__int64 __fastcall sub_6500BB78(__int64 a1, __int64 a2, int a3)
{
  __int64 v4; // x3
  int v5; // w5
  int v6; // w4
  __int64 result; // x0

  v4 = 0LL;
  while ( a3 != (_DWORD)v4 )
  {
    v5 = *(unsigned __int8 *)(a1 + v4);
    v6 = *(unsigned __int8 *)(a2 + v4++);
    result = (unsigned int)(v5 - v6);
    if ( v5 != v6 )
      return result;
  }
  return 0LL;
}

//----- (000000006500BBA8) ----------------------------------------------------
_BYTE *__fastcall sub_6500BBA8(_BYTE *a1, int n48, __int64 a3)
{
  if ( a1 )
  {
    if ( a3 )
      return (_BYTE *)sub_6500D0B4(a1, n48, a3);
  }
  return a1;
}

//----- (000000006500BBBC) ----------------------------------------------------
__int64 __fastcall sub_6500BBBC(__int64 a1, _QWORD *a2)
{
  __int64 v2; // x3
  __int64 v3; // x2
  __int64 n596; // x3
  __int64 v5; // x2
  __int64 v6; // x0
  __int64 result; // x0

  v2 = a1 + *(unsigned int *)(a1 + 48) + 512LL;
  v3 = *(_QWORD *)(v2 + 40);
  n596 = *(_QWORD *)(v2 + 32);
  v5 = a1 + v3;
  if ( n596 == 1112 )
  {
    v6 = *(_QWORD *)(v5 + 592);
  }
  else
  {
    result = 0LL;
    if ( n596 != 596 )
      return result;
    v6 = *(unsigned int *)(v5 + 336);
  }
  *a2 = v6;
  return 1LL;
}

//----- (000000006500BC00) ----------------------------------------------------
// write access to const memory has been detected, the output may be wrong!
__int64 sub_6500BC00()
{
  __int64 n0x65011000; // x0
  unsigned __int64 n0x65011068; // x3
  __int64 v2; // t1
  __int64 v3; // [xsp+28h] [xbp+28h] BYREF

  v3 = 0LL;
  n0x65011000 = sub_6500BBBC(1694500864LL, &v3);
  if ( (_DWORD)n0x65011000 )
  {
    n0x65011000 = 0x65011000uLL;
    if ( MEMORY[0x650110B8] )
    {
      n0x65011068 = 0x65011068uLL;
      n0x65011000 = 1LL;
      if ( MEMORY[0x65011068] == v3 )
      {
        while ( (_DWORD)n0x65011000 != MEMORY[0x650110B8] )
        {
          v2 = *(_QWORD *)(n0x65011068 + 8);
          n0x65011068 += 8LL;
          if ( MEMORY[0x65011068] != v2 )
            return n0x65011000;
          n0x65011000 = (unsigned int)(n0x65011000 + 1);
        }
        n0x65011000 = (__int64)&unk_65010000;
        if ( MEMORY[0x65011068] > 0x1234uLL )
          n0x65011000 = sub_6500BAD8(1, MEMORY[0x65011068]);
        MEMORY[0x650110B8] = 0;
        qword_65010210[0] = 0LL;
      }
    }
  }
  return n0x65011000;
}
// 6500BC9C: write access to const memory at 65010210 has been detected
// 65010210: using guessed type unsigned __int64 qword_65010210[153];

//----- (000000006500BCAC) ----------------------------------------------------
__int64 __fastcall sub_6500BCAC(int a1, unsigned __int64 n0x1234)
{
  int v4; // w0

  if ( a1 != 1 )
  {
    if ( !a1 )
      sub_6500B910(2, (unsigned __int64 *)&qword_65010208);
    return 0LL;
  }
  sub_6500B910(1, qword_65010210);
  if ( n0x1234 < 0x1234 )
    return 0LL;
  v4 = MEMORY[0x650110B8];
  *(_QWORD *)(0x65011068 + 8LL * MEMORY[0x650110B8]) = n0x1234;
  MEMORY[0x650110B8] = v4 + 1;
  return 1LL;
}
// 6500BD50: conditional instruction was optimized away because x19.8>=FFFFu
// 65010208: using guessed type __int64 qword_65010208;
// 65010210: using guessed type unsigned __int64 qword_65010210[153];

//----- (000000006500BD84) ----------------------------------------------------
__int64 __fastcall sub_6500BD84(__int64 a1, __int64 a2, unsigned __int8 *a3)
{
  unsigned int v5; // w1
  unsigned int v7; // w21
  _BYTE *v8; // x0
  _BYTE *v9; // x23
  unsigned int v10; // w0
  unsigned int v11; // w0
  int n384_2; // w0
  unsigned __int64 n0x1234; // x1
  unsigned int v15; // w0
  unsigned int v16; // w0
  int n384; // w0
  __int64 v19; // [xsp+60h] [xbp+60h] BYREF
  __int64 v20; // [xsp+68h] [xbp+68h]
  __int64 v21; // [xsp+70h] [xbp+70h]
  __int64 v22; // [xsp+78h] [xbp+78h]
  int v23; // [xsp+84h] [xbp+84h] BYREF
  unsigned __int8 *v24; // [xsp+88h] [xbp+88h] BYREF
  _BYTE *v25; // [xsp+90h] [xbp+90h]
  unsigned int n48; // [xsp+98h] [xbp+98h]
  int n384_1; // [xsp+9Ch] [xbp+9Ch]
  _BYTE v28[32]; // [xsp+A0h] [xbp+A0h] BYREF
  __int64 v29; // [xsp+C0h] [xbp+C0h]
  __int64 v30; // [xsp+C8h] [xbp+C8h]
  __int64 v31; // [xsp+D0h] [xbp+D0h]
  __int64 v32; // [xsp+D8h] [xbp+D8h]
  _QWORD v33[4]; // [xsp+E0h] [xbp+E0h] BYREF
  _BYTE v34[380]; // [xsp+100h] [xbp+100h] BYREF
  _BYTE v35[128]; // [xsp+27Ch] [xbp+27Ch] BYREF
  _BYTE v36[4]; // [xsp+2FCh] [xbp+2FCh] BYREF

  v23 = 1;
  LODWORD(v29) = 21;
  v5 = *a3;
  v30 = 0x200000402000004LL;
  memset(v33, 0, sizeof(v33));
  LODWORD(v31) = 32;
  if ( v5 > 1 )
    return 0;
  v8 = a3 + 4;
  v9 = a3 + 524;
  if ( v5 != 1 )
  {
    sub_6500BBA8(v8, 520, (__int64)v28);
    if ( !(unsigned int)sub_6500BB78(a2, (__int64)v9, 32) )
    {
      v7 = sub_6500BB78(a1, (__int64)v28, 32);
      if ( !v7 && *((_DWORD *)a3 + 139) == 1 )
      {
        if ( *((_DWORD *)a3 + 1) == 3072 )
        {
          sub_6500BB3C((__int64)v34, v7, 384);
          sub_6500BB58((__int64)v35, (__int64)(a3 + 8), 4);
          v15 = *((_DWORD *)a3 + 1);
          v24 = a3 + 12;
          v25 = v34;
          n48 = v15 >> 3;
          n384 = 384;
        }
        else
        {
          sub_6500BB3C((__int64)v34, v7, 512);
          sub_6500BB58((__int64)v36, (__int64)(a3 + 8), 4);
          v16 = *((_DWORD *)a3 + 1);
          v24 = a3 + 12;
          v25 = v34;
          n48 = v16 >> 3;
          n384 = 512;
        }
        n384_1 = n384;
        sub_6500BBA8(v9, 44, (__int64)v33);
        v19 = v29;
        v20 = v30;
        v21 = v31;
        v22 = v32;
        if ( !(unsigned int)sub_6500CEF4((__int64)&v24, (__int64)v33, 0x20u, a3 + 568, n48, (int *)&v19, &v23) && !v23 )
        {
          n0x1234 = *((_QWORD *)a3 + 70);
          return (unsigned int)sub_6500BCAC(1, n0x1234);
        }
        return v7;
      }
    }
    return 0;
  }
  sub_6500BBA8(v8, 520, (__int64)v28);
  if ( (unsigned int)sub_6500BB78(a2, (__int64)v9, 32) )
    return 0;
  v7 = sub_6500BB78(a1, (__int64)v28, 32);
  if ( v7 || *((_DWORD *)a3 + 147) != 1 )
    return 0;
  if ( *((_DWORD *)a3 + 1) == 3072 )
  {
    sub_6500BB3C((__int64)v34, v7, 384);
    sub_6500BB58((__int64)v35, (__int64)(a3 + 8), 4);
    v10 = *((_DWORD *)a3 + 1);
    v24 = a3 + 12;
    v25 = v34;
    n48 = v10 >> 3;
    n384_2 = 384;
  }
  else
  {
    sub_6500BB3C((__int64)v34, v7, 512);
    sub_6500BB58((__int64)v36, (__int64)(a3 + 8), 4);
    v11 = *((_DWORD *)a3 + 1);
    v24 = a3 + 12;
    v25 = v34;
    n48 = v11 >> 3;
    n384_2 = 512;
  }
  n384_1 = n384_2;
  sub_6500BBA8(v9, 76, (__int64)v33);
  v19 = v29;
  v20 = v30;
  v21 = v31;
  v22 = v32;
  if ( !(unsigned int)sub_6500CEF4((__int64)&v24, (__int64)v33, 0x20u, a3 + 600, n48, (int *)&v19, &v23) && !v23 )
  {
    n0x1234 = *((_QWORD *)a3 + 74);
    return (unsigned int)sub_6500BCAC(1, n0x1234);
  }
  return v7;
}

//----- (000000006500C080) ----------------------------------------------------
__int64 __fastcall sub_6500C080(__int64 a1)
{
  return a1 + *(_QWORD *)(a1 + *(unsigned int *)(a1 + 48) + 536);
}

//----- (000000006500C094) ----------------------------------------------------
__int64 __fastcall sub_6500C094(__int64 a1)
{
  return a1 + *(_QWORD *)(a1 + *(unsigned int *)(a1 + 48) + 552);
}

//----- (000000006500C0A8) ----------------------------------------------------
__int64 __fastcall sub_6500C0A8(__int64 a1, __int64 a2)
{
  __int64 v3; // x21
  _BYTE *v5; // x0
  unsigned __int8 *v6; // x0
  _BYTE v8[32]; // [xsp+30h] [xbp+30h] BYREF

  v3 = a2 + *(unsigned int *)(a2 + 48) + 512LL;
  v5 = (_BYTE *)sub_6500C080(a2);
  sub_6500BBA8(v5, *(_DWORD *)(v3 + 16), (__int64)v8);
  v6 = (unsigned __int8 *)sub_6500C094(a2);
  return sub_6500BD84(a1, (__int64)v8, v6);
}

//----- (000000006500C108) ----------------------------------------------------
__int64 __fastcall sub_6500C108(__int64 a1, __int64 a2)
{
  __int64 v3; // x1

  if ( *(_QWORD *)(*(unsigned int *)(a1 + 48) + a1 + 544) == 596LL )
    v3 = sub_6500C094(a1) + 300;
  else
    v3 = sub_6500C094(a1) + 556;
  return sub_6500BB58(a2, v3, 32);
}

//----- (000000006500C154) ----------------------------------------------------
bool __fastcall sub_6500C154(__int64 a1, __int64 a2)
{
  __int64 v5; // [xsp+20h] [xbp+20h] BYREF
  _BYTE v6[32]; // [xsp+30h] [xbp+30h] BYREF

  sub_6500BB3C((__int64)v6, 0, 32);
  sub_6500C108(a1, (__int64)v6);
  sub_6500BB3C((__int64)&v5, 0, 16);
  return (unsigned int)sub_6500C0A8((__int64)v6, a2) == 0;
}

//----- (000000006500C1B4) ----------------------------------------------------
_BYTE *__fastcall sub_6500C1B4(_BYTE *result)
{
  result[3] = 119;
  result[2] = 48;
  result[1] = 16;
  *result = 8;
  return result;
}

//----- (000000006500C1E0) ----------------------------------------------------
__int64 __fastcall sub_6500C1E0(unsigned __int16 a1, unsigned int a2, __int64 a3)
{
  __int64 v3; // x3
  __int64 result; // x0

  v3 = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 1280LL) = a1 | 0x10000000 | ((a2 & 0x1F) << 16);
  for ( result = 0LL; a2 > (unsigned int)result; ++result )
    *(_DWORD *)(v3 + 1280) = *(_DWORD *)(a3 + 4 * result);
  return result;
}

//----- (000000006500C220) ----------------------------------------------------
__int64 __fastcall sub_6500C220(_DWORD *a1)
{
  __int64 v1; // x1
  __int64 result; // x0

  v1 = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 1280LL) = ((*a1 & 0x7000000u) >> 4) | 0x20000000 | ((unsigned __int8)a1[5] << 8);
  *(_DWORD *)(v1 + 1280) = a1[1];
  *(_DWORD *)(v1 + 1280) = a1[2];
  *(_DWORD *)(v1 + 1280) = a1[3];
  result = (unsigned int)a1[4];
  *(_DWORD *)(v1 + 1280) = result;
  return result;
}

//----- (000000006500C270) ----------------------------------------------------
__int64 __fastcall sub_6500C270(unsigned __int16 a1, unsigned int a2, __int64 a3)
{
  __int64 v3; // x3
  __int64 result; // x0

  v3 = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 1536LL) = a1 | 0x10000000 | ((a2 & 0x1F) << 16);
  for ( result = 0LL; a2 > (unsigned int)result; ++result )
    *(_DWORD *)(v3 + 1536) = *(_DWORD *)(a3 + 4 * result);
  return result;
}

//----- (000000006500C2B0) ----------------------------------------------------
__int64 __fastcall sub_6500C2B0(_DWORD *a1, int a2)
{
  __int64 v2; // x2
  __int64 result; // x0

  v2 = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 1536LL) = ((*a1 & 0x7000000u) >> 4) | 0x20000000;
  result = (unsigned int)a1[1];
  *(_DWORD *)(v2 + 1536) = result;
  *(_DWORD *)(v2 + 1536) = a2;
  return result;
}

//----- (000000006500C2E0) ----------------------------------------------------
__int64 __fastcall sub_6500C2E0(int *a1)
{
  __int64 v2; // x0
  __int64 v3; // x0
  int v4; // w1
  unsigned int v6; // [xsp+28h] [xbp+28h] BYREF
  unsigned int v7; // [xsp+2Ch] [xbp+2Ch]
  int v8; // [xsp+30h] [xbp+30h]
  int v9; // [xsp+34h] [xbp+34h]
  int v10; // [xsp+38h] [xbp+38h] BYREF
  __int16 v11; // [xsp+3Ch] [xbp+3Ch]
  _BYTE v12[4]; // [xsp+40h] [xbp+40h] BYREF
  int v13; // [xsp+44h] [xbp+44h]
  int v14; // [xsp+48h] [xbp+48h]
  char v15; // [xsp+4Ch] [xbp+4Ch]

  sub_6500BB3C((__int64)&v6, 0, 16);
  v2 = *((_QWORD *)a1 + 21);
  HIBYTE(v6) = HIBYTE(v6) & 0xF0 | BYTE4(v2) & 0xF;
  v8 = v2;
  v6 = v6 & 0xFF000000 | a1[44] & 0xFFFFFF;
  v3 = *((_QWORD *)a1 + 23);
  v9 = v3;
  HIBYTE(v7) = HIBYTE(v7) & 0xF0 | BYTE4(v3) & 0xF;
  v7 = v7 & 0xFF000000 | a1[48] & 0xFFFFFF;
  sub_6500C1E0(0x48u, 4u, (__int64)&v6);
  sub_6500BB3C((__int64)&v10, 0, 24);
  v10 = *a1;
  sub_6500C1B4(v12);
  v4 = *a1;
  v12[0] = v12[0] & 0xEF | (16 * (a1[2] & 1)) | 6;
  v11 = v11 & 0xFE0F | (16 * (v4 & 0x1F)) | 1;
  v15 = 0;
  v13 = 0;
  v14 = 0;
  return sub_6500C220(&v10);
}

//----- (000000006500C3D0) ----------------------------------------------------
__int64 __fastcall sub_6500C3D0(int *a1)
{
  __int64 v2; // x2
  unsigned int v3; // w0
  __int64 v4; // x3
  unsigned int v5; // w1
  __int64 v6; // x4
  int v7; // w5
  __int64 v8; // x6
  __int64 v9; // x3
  unsigned int v10; // w1
  unsigned int v11; // w0
  __int16 *v12; // x2
  __int64 v13; // x4
  int v14; // w5
  __int64 v15; // x6
  unsigned __int16 n832; // w0
  unsigned int v17; // w1
  __int16 v18; // w1
  __int16 v19; // w0
  unsigned int n0xB; // w0
  __int64 v21; // x0
  int v22; // w2
  int v23; // w1
  int v24; // w2
  int v25; // w1
  int v26; // w2
  __int64 v27; // x0
  __int64 v28; // x0
  char v29; // w0
  char v30; // w2
  char v31; // w0
  int n16; // w1
  char n2; // w1
  int v34; // w1
  int v35; // w1
  __int16 v37; // [xsp+20h] [xbp+20h] BYREF
  __int16 v38; // [xsp+22h] [xbp+22h]
  _DWORD v39[4]; // [xsp+28h] [xbp+28h] BYREF
  unsigned int v40; // [xsp+38h] [xbp+38h] BYREF
  unsigned int v41; // [xsp+3Ch] [xbp+3Ch]
  int v42; // [xsp+40h] [xbp+40h]
  int v43; // [xsp+44h] [xbp+44h]
  int n12; // [xsp+48h] [xbp+48h] BYREF
  char v45; // [xsp+4Ch] [xbp+4Ch]
  char v46; // [xsp+4Dh] [xbp+4Dh]
  char v47; // [xsp+50h] [xbp+50h] BYREF
  char v48; // [xsp+51h] [xbp+51h]
  int v49; // [xsp+54h] [xbp+54h]
  int v50; // [xsp+58h] [xbp+58h]
  char v51; // [xsp+5Ch] [xbp+5Ch]
  _BYTE v52[32]; // [xsp+60h] [xbp+60h] BYREF

  if ( (unsigned int)*a1 <= 0xC )
  {
    v2 = 1LL << *a1;
    if ( (v2 & 0x37) == 0 )
    {
      v3 = v2 & 0x1000;
      if ( (v2 & 0x1000) != 0 )
      {
        sub_6500BB3C((__int64)&v37, 0, 4);
        v18 = a1[8];
        v37 = v37 & 0xF801 | 0x88;
        v12 = &v37;
        v19 = v38 & 0xC01F | (32 * (v18 & 0x1FF));
        v17 = 1;
        v38 = v19;
        n832 = 832;
LABEL_17:
        sub_6500C1E0(n832, v17, (__int64)v12);
        goto LABEL_18;
      }
      if ( (v2 & 8) == 0 )
        goto LABEL_18;
      v4 = *((_QWORD *)a1 + 5);
      if ( v4 )
      {
        v5 = a1[12];
        if ( v5 )
        {
          while ( v3 < v5 )
          {
            v6 = v3 & 0xFFFFFFFC;
            v7 = *(unsigned __int8 *)(v4 + v3 + 3) | (*(unsigned __int8 *)(v4 + v3) << 24) | (*(unsigned __int8 *)(v4 + v3 + 1) << 16);
            v8 = v3 + 2;
            v3 += 4;
            *(_DWORD *)&v52[v6] = v7 | (*(unsigned __int8 *)(v4 + v8) << 8);
          }
          sub_6500C1E0(0xA0u, v5 >> 2, (__int64)v52);
        }
      }
    }
    v9 = *((_QWORD *)a1 + 3);
    if ( v9 )
    {
      v10 = a1[8];
      if ( v10 )
      {
        v11 = 0;
        v12 = (__int16 *)v52;
        while ( v11 < v10 )
        {
          v13 = v11 & 0xFFFFFFFC;
          v14 = *(unsigned __int8 *)(v9 + v11 + 3) | (*(unsigned __int8 *)(v9 + v11) << 24) | (*(unsigned __int8 *)(v9 + v11 + 1) << 16);
          v15 = v11 + 2;
          v11 += 4;
          *(_DWORD *)&v52[v13] = v14 | (*(unsigned __int8 *)(v9 + v15) << 8);
        }
        n832 = 128;
        v17 = v10 >> 2;
        goto LABEL_17;
      }
    }
  }
LABEL_18:
  if ( !a1[1] )
  {
    sub_6500BB3C((__int64)v39, 0, 16);
    n0xB = *a1 - 1;
    if ( n0xB <= 0xB && ((1LL << n0xB) & 0x817) != 0 )
    {
      v21 = *((_QWORD *)a1 + 7);
      v22 = *(unsigned __int8 *)(v21 + 4);
      v39[0] = _byteswap_ulong(*(_DWORD *)v21);
      v23 = (*(unsigned __int8 *)(v21 + 5) << 16) | (v22 << 24) | *(unsigned __int8 *)(v21 + 7) | (*(unsigned __int8 *)(v21 + 6) << 8);
      v24 = *(unsigned __int8 *)(v21 + 8);
      v39[1] = v23;
      v25 = (*(unsigned __int8 *)(v21 + 9) << 16) | (v24 << 24) | *(unsigned __int8 *)(v21 + 11) | (*(unsigned __int8 *)(v21 + 10) << 8);
      v26 = *(unsigned __int8 *)(v21 + 12);
      v39[2] = v25;
      v39[3] = (*(unsigned __int8 *)(v21 + 13) << 16) | (v26 << 24) | *(unsigned __int8 *)(v21 + 15) | (*(unsigned __int8 *)(v21 + 14) << 8);
    }
    sub_6500C1E0(0x70u, 4u, (__int64)v39);
  }
  sub_6500BB3C((__int64)&v40, 0, 16);
  v27 = *((_QWORD *)a1 + 21);
  HIBYTE(v40) = HIBYTE(v40) & 0xF0 | BYTE4(v27) & 0xF;
  v42 = v27;
  v40 = v40 & 0xFF000000 | a1[44] & 0xFFFFFF;
  v28 = *((_QWORD *)a1 + 23);
  v43 = v28;
  HIBYTE(v41) = HIBYTE(v41) & 0xF0 | BYTE4(v28) & 0xF;
  v41 = v41 & 0xFF000000 | a1[48] & 0xFFFFFF;
  sub_6500C1E0(0x48u, 4u, (__int64)&v40);
  sub_6500BB3C((__int64)&n12, 0, 24);
  n12 = *a1;
  sub_6500C1B4(&v47);
  if ( n12 == 12 )
    v29 = v48 | 2;
  else
    v29 = v48 & 0xFE;
  v48 = v29;
  v30 = v46;
  v47 = v47 & 0xEF | (16 * (a1[2] & 1)) | 6;
  v31 = v46 & 0xBF;
  v46 &= ~0x40u;
  n16 = a1[8];
  if ( n16 == 24 )
  {
    n2 = 1;
  }
  else
  {
    if ( n16 == 16 )
    {
      v46 = v30 & 0x8F;
      goto LABEL_32;
    }
    n2 = 2;
  }
  v46 = v31 & 0xCF | (16 * (n2 & 3));
LABEL_32:
  LOBYTE(v34) = 1;
  if ( n12 != 12 )
    v34 = *a1;
  v46 = v46 & 0xF8 | v34 & 7;
  v35 = a1[4];
  v51 = 0;
  v45 = v45 & 0xEF | (16 * (v35 & 1)) | 1;
  v49 = 0;
  v50 = 0;
  return sub_6500C220(&n12);
}

//----- (000000006500C744) ----------------------------------------------------
__int64 __fastcall sub_6500C744(int *a1)
{
  _DWORD *v2; // x20
  __int64 v3; // x20
  __int64 v4; // x20
  __int64 v5; // x0
  __int64 v6; // x1
  int v8; // [xsp+2Ch] [xbp+2Ch] BYREF
  _DWORD v9[3]; // [xsp+30h] [xbp+30h] BYREF
  int v10; // [xsp+3Ch] [xbp+3Ch] BYREF
  int v11; // [xsp+40h] [xbp+40h] BYREF
  int v12; // [xsp+48h] [xbp+48h] BYREF
  char v13; // [xsp+4Ch] [xbp+4Ch]
  char v14; // [xsp+4Eh] [xbp+4Eh]
  char v15; // [xsp+4Fh] [xbp+4Fh]
  _BYTE v16[2]; // [xsp+50h] [xbp+50h] BYREF
  char v17; // [xsp+52h] [xbp+52h]

  v2 = (_DWORD *)*((_QWORD *)a1 + 2);
  *v2 = sub_6500D18C(0, 0);
  v3 = *((_QWORD *)a1 + 2);
  *(_DWORD *)(v3 + 4) = sub_6500D13C(0, 0, 1, a1[46] & 0x1FFFF);
  v4 = *((_QWORD *)a1 + 2);
  *(_DWORD *)(v4 + 8) = sub_6500D10C();
  v8 = a1[48];
  sub_6500C270(0xD0u, 1u, (__int64)&v8);
  sub_6500BB3C((__int64)v9, 0, 20);
  v10 = (*((_QWORD *)a1 + 23) >> 17) & 0x7FFFF;
  v5 = *((_QWORD *)a1 + 2);
  v9[2] = v5;
  HIBYTE(v9[0]) = HIBYTE(v9[0]) & 0xF0 | BYTE4(v5) & 0xF;
  v9[0] = v9[0] & 0xFF000000 | 0xC;
  v11 = 0;
  sub_6500C270(0x610u, 1u, (__int64)&v11);
  sub_6500C270(0x60Cu, 1u, (__int64)&v10);
  sub_6500BB3C((__int64)&v12, 0, 24);
  v12 = *a1;
  sub_6500C1B4(v16);
  v17 = v17 & 0x3F | 0x80;
  v16[0] |= 0x16u;
  v14 = -1;
  v13 |= 3u;
  v15 = v15 & 0xF0 | 3;
  v6 = *((_QWORD *)a1 + 2);
  v15 = v15 & 0xF | (16 * (BYTE4(v6) & 0xF));
  return sub_6500C2B0(&v12, v6);
}

//----- (000000006500C89C) ----------------------------------------------------
__int64 sub_6500C89C()
{
  while ( (*(_DWORD *)(MEMORY[0x650110C8] + 32LL) & 1) == 0 )
    ;
  *(_DWORD *)(MEMORY[0x650110C8] + 36LL) = *(_DWORD *)(MEMORY[0x650110C8] + 32LL);
  return 0LL;
}

//----- (000000006500C8C0) ----------------------------------------------------
__int64 sub_6500C8C0()
{
  while ( (*(_DWORD *)(MEMORY[0x650110C8] + 32LL) & 0x2000) == 0 )
    ;
  *(_DWORD *)(MEMORY[0x650110C8] + 36LL) = *(_DWORD *)(MEMORY[0x650110C8] + 32LL);
  return 0LL;
}

//----- (000000006500C8E4) ----------------------------------------------------
__int64 __fastcall sub_6500C8E4(int *a1)
{
  int v2; // w0
  __int64 v3; // x3
  int v5; // [xsp+3Ch] [xbp+3Ch] BYREF
  __int64 v6; // [xsp+40h] [xbp+40h] BYREF
  int v7; // [xsp+4Ch] [xbp+4Ch] BYREF
  int v8; // [xsp+50h] [xbp+50h] BYREF
  int v9; // [xsp+58h] [xbp+58h] BYREF
  char v10; // [xsp+5Ch] [xbp+5Ch]
  char v11; // [xsp+5Eh] [xbp+5Eh]
  char v12; // [xsp+5Fh] [xbp+5Fh]

  v5 = a1[8];
  sub_6500C270(0xD0u, 1u, (__int64)&v5);
  sub_6500BB3C((__int64)&v6, 0, 20);
  v7 = (*((_QWORD *)a1 + 23) >> 17) & 0x7FFFF;
  sub_6500C270(0x60Cu, 1u, (__int64)&v7);
  sub_6500BB3C((__int64)&v9, 0, 24);
  v9 = *a1;
  v11 = -1;
  v10 |= 3u;
  v12 = (16 * (((unsigned __int64)(a1 + 9) >> 32) & 0xF)) | 3;
  v8 = (*((_QWORD *)a1 + 2) >> 17) & 0x7FFFF;
  sub_6500C270(0x610u, 1u, (__int64)&v8);
  a1[9] = sub_6500D114(0, 0, 1, a1[4] & 0x1FFFF);
  a1[10] = sub_6500D10C();
  sub_6500C2B0(&v9, (_DWORD)a1 + 36);
  sub_6500C8C0();
  v8 = (*((_QWORD *)a1 + 3) >> 17) & 0x7FFFF;
  sub_6500C270(0x610u, 1u, (__int64)&v8);
  a1[9] = sub_6500D114(0, 2, 1, a1[6] & 0x1FFFF);
  a1[10] = sub_6500D10C();
  sub_6500C2B0(&v9, (_DWORD)a1 + 36);
  sub_6500C8C0();
  v8 = (*((_QWORD *)a1 + 21) >> 17) & 0x7FFFF;
  sub_6500C270(0x610u, 1u, (__int64)&v8);
  a1[9] = sub_6500D114(0, 1, 1, a1[42] & 0x1FFFF);
  v2 = sub_6500D164(0, 3, 1, 2);
  v3 = *((_QWORD *)a1 + 23) & 0x1FFFFLL;
  a1[10] = v2;
  a1[11] = sub_6500D13C(0, 3, 1, v3);
  a1[12] = sub_6500D10C();
  return sub_6500C2B0(&v9, (int)a1 + 36);
}

//----- (000000006500CAB8) ----------------------------------------------------
int *__fastcall sub_6500CAB8(int *a1)
{
  unsigned int n0x2000000; // w1

  n0x2000000 = *a1 & 0xFFFF0000;
  if ( n0x2000000 == 0x2000000 )
    return (int *)sub_6500C2E0(a1);
  if ( n0x2000000 > 0x2000000 )
  {
    if ( n0x2000000 == 83951616 )
    {
      return (int *)sub_6500C8E4(a1);
    }
    else if ( n0x2000000 == 84148224 )
    {
      return (int *)sub_6500C744(a1);
    }
  }
  else if ( !n0x2000000 )
  {
    return (int *)sub_6500C3D0(a1);
  }
  return a1;
}

//----- (000000006500CB00) ----------------------------------------------------
__int64 sub_6500CB00()
{
  __int64 result; // x0

  result = MEMORY[0x650110C0];
  *(_DWORD *)(MEMORY[0x650110C0] + 0x2000LL) = 0x40000000;
  return result;
}

//----- (000000006500CB18) ----------------------------------------------------
__int64 sub_6500CB18()
{
  __int64 v0; // x0
  __int64 result; // x0

  v0 = MEMORY[0x650110C0];
  *(_DWORD *)(MEMORY[0x650110C0] + 4100LL) = 0x100000;
  *(_DWORD *)(v0 + 8196) = 0x100000;
  result = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 44LL) = 1;
  return result;
}

//----- (000000006500CB50) ----------------------------------------------------
__int64 __fastcall sub_6500CB50(int *a1)
{
  int v1; // w0
  __int64 result; // x0

  v1 = *a1;
  if ( (v1 & 3) != 3 )
    MEMORY[0x20010118] = v1 | 3;
  *(_DWORD *)(MEMORY[0x650110C0] + 4096LL) = 0x40000000;
  sub_6500CB18();
  result = MEMORY[0x650110C8];
  *(_DWORD *)(MEMORY[0x650110C8] + 36LL) = 32657;
  return result;
}

//----- (000000006500CBB0) ----------------------------------------------------
__int64 sub_6500CBB0()
{
  __int64 result; // x0

  result = MEMORY[0x650110C0];
  *(_DWORD *)(MEMORY[0x650110C0] + 4100LL) = 0x200000;
  *(_DWORD *)(result + 8196) = 0x200000;
  return result;
}

//----- (000000006500CBCC) ----------------------------------------------------
unsigned __int64 sub_6500CBCC()
{
  unsigned __int64 n0x650110C8; // x0

  MEMORY[0x650110C0] = 537919488LL;
  sub_6500CBB0();
  n0x650110C8 = 0x650110C8uLL;
  MEMORY[0x650110C8] = 539951104LL;
  return n0x650110C8;
}

//----- (000000006500CC04) ----------------------------------------------------
__int64 __fastcall sub_6500CC04(
        unsigned int n0x2000004,
        unsigned int a2,
        _BYTE *a3,
        int n48_1,
        __int64 a5,
        int *a6,
        _BYTE *a7,
        int n32,
        _BYTE *a9,
        int n384,
        __int64 a11,
        int n16)
{
  bool v19; // zf
  unsigned __int64 v20; // x0
  unsigned int n84148224; // w4
  int v22; // w0
  unsigned int v23; // w20
  __int64 v24; // x0
  _BYTE v27[128]; // [xsp+78h] [xbp+78h] BYREF
  int v28[4]; // [xsp+F8h] [xbp+F8h] BYREF
  _BYTE *v29; // [xsp+108h] [xbp+108h]
  _BYTE *v30; // [xsp+110h] [xbp+110h]
  int n32_1; // [xsp+118h] [xbp+118h]
  __int64 v32; // [xsp+120h] [xbp+120h]
  int v33; // [xsp+128h] [xbp+128h]
  __int64 v34; // [xsp+130h] [xbp+130h]
  int n16_1; // [xsp+138h] [xbp+138h]
  _BYTE *v36; // [xsp+1A0h] [xbp+1A0h]
  int n48_2; // [xsp+1A8h] [xbp+1A8h]
  __int64 v38; // [xsp+1B0h] [xbp+1B0h]
  int n48; // [xsp+1B8h] [xbp+1B8h]

  sub_65002000((__int64)v27, 0, 128LL);
  if ( !a3 || !n48_1 || !a5 )
    return 4294901767LL;
  if ( n0x2000004 <= 0x2000005 )
  {
    if ( n0x2000004 >= 0x2000004 )
      goto LABEL_40;
    if ( n0x2000004 == 1 )
      goto LABEL_17;
    if ( n0x2000004 )
    {
      if ( n0x2000004 != 12 )
        return 4294901761LL;
LABEL_18:
      if ( !a11 || n16 != 16 )
        return 4294901767LL;
      if ( n0x2000004 == 12 )
        sub_6500B2F0();
      goto LABEL_40;
    }
    if ( a2 <= 1 )
    {
LABEL_17:
      if ( !a7 || (n32 & 0xFFFFFFF7) != 0x10 && n32 != 32 )
        return 4294901767LL;
      goto LABEL_18;
    }
    return 4294901767LL;
  }
  if ( n0x2000004 == 84082688 )
    goto LABEL_40;
  if ( n0x2000004 == 84148224 )
  {
    if ( !a6 || !*a6 )
      return 4294901767LL;
    goto LABEL_40;
  }
  if ( n0x2000004 != 83951616 )
    return 4294901761LL;
  switch ( n32 )
  {
    case 384:
      if ( !a7 || !a9 )
        return 4294901767LL;
      v19 = n384 == 384;
      break;
    case 512:
      if ( !a7 || !a9 )
        return 4294901767LL;
      v19 = n384 == 512;
      break;
    case 256:
      if ( !a7 || !a9 )
        return 4294901767LL;
      v19 = n384 == 256;
      break;
    default:
      goto LABEL_40;
  }
  if ( !v19 )
    return 4294901767LL;
LABEL_40:
  if ( MEMORY[0x650110D0] == 1 )
    return 4294901760LL;
  v20 = sub_6500CBCC();
  sub_6500CB48(v20);
  MEMORY[0x650110D0] = 1;
  sub_6500BB3C((__int64)v28, 0, 200);
  v38 = a5;
  n84148224 = n0x2000004 & 0xFFFF0000;
  v28[0] = n0x2000004;
  v28[1] = 0;
  v28[2] = 1;
  v36 = a3;
  n48_2 = n48_1;
  if ( (n0x2000004 & 0xFFFF0000) == 0x5010000 )
  {
    v29 = a7;
    v30 = a9;
    n32_1 = n32;
  }
  else
  {
    if ( n84148224 > 0x5010000 )
    {
      if ( n84148224 == 84082688 )
      {
        v29 = a3;
      }
      else if ( n84148224 == 84148224 )
      {
        n48 = *a6;
        v29 = v27;
      }
      goto LABEL_53;
    }
    if ( n84148224 )
    {
      if ( n84148224 == 0x2000000 )
        n48 = 48;
      goto LABEL_53;
    }
    v34 = a11;
    LODWORD(v29) = a2;
    v30 = a7;
    n32_1 = n32;
    v32 = 0LL;
    v33 = 0;
    n16_1 = n16;
  }
  n48 = n48_1;
LABEL_53:
  sub_6500CAB8(v28);
  if ( (n0x2000004 & 0xFF000000) == 0x5000000 )
    v22 = sub_6500C8C0();
  else
    v22 = sub_6500C89C();
  v23 = 0;
  if ( v22 )
    v23 = -65536;
  if ( a6 )
    *a6 = n48;
  if ( n0x2000004 == 12 )
    sub_6500B358();
  MEMORY[0x650110D0] = 0;
  v24 = sub_6500CB00();
  nullsub_3(v24);
  return v23;
}
// 6500CB48: using guessed type __int64 __fastcall sub_6500CB48(_QWORD);
// 6500CC00: using guessed type __int64 __fastcall nullsub_3(_QWORD);

//----- (000000006500CEF4) ----------------------------------------------------
__int64 __fastcall sub_6500CEF4(
        __int64 a1,
        __int64 a2,
        unsigned int n512_2,
        _BYTE *n0x650110D8,
        int n48,
        int *a6,
        int *a7)
{
  _BYTE *a9; // x0
  __int64 result; // x0
  int n21; // w0
  int n512_1; // w0
  unsigned __int8 *n0x65011120_1; // x0
  unsigned int n0xA; // w3
  int i; // [xsp+64h] [xbp+44h] BYREF
  int n512; // [xsp+68h] [xbp+48h] BYREF
  int v20; // [xsp+6Ch] [xbp+4Ch] BYREF
  unsigned __int8 n0x65011120[512]; // [xsp+70h] [xbp+50h] BYREF
  _BYTE n0x65011120_2[512]; // [xsp+270h] [xbp+250h] BYREF

  if ( !a1 )
    return 4294901762LL;
  if ( !a2 || !n0x650110D8 || !n48 || !a7 )
    return 4294901767LL;
  a9 = *(_BYTE **)(a1 + 8);
  *a7 = 1;
  result = sub_6500CC04(
             0x5010000u,
             0,
             n0x650110D8,
             n48,
             (__int64)n0x65011120,
             &i,
             *(_BYTE **)a1,
             *(_DWORD *)(a1 + 16),
             a9,
             *(_DWORD *)(a1 + 20),
             0LL,
             n48 == 0);
  if ( (_DWORD)result )
    return result;
  n21 = *a6;
  if ( *a6 == 20 )
  {
    n0xA = *(_DWORD *)(a1 + 16);
    n512 = 512;
    result = sub_6500D640(n0x65011120, i, 1u, n0xA, (__int64)n0x65011120_2, (unsigned int *)&n512, &v20);
    if ( (_DWORD)result || !v20 )
    {
      *a7 = 1;
      return result;
    }
    if ( !n512_2 )
    {
      sub_6500BB58(a2, (__int64)n0x65011120_2, n512);
      n512_1 = n512;
      goto LABEL_21;
    }
    if ( n512 != n512_2 )
      goto LABEL_14;
    n0x65011120_1 = n0x65011120_2;
LABEL_13:
    if ( !(unsigned int)sub_6500BB78((__int64)n0x65011120_1, a2, n512_2) )
    {
      *a7 = 0;
      return 0LL;
    }
LABEL_14:
    n512_1 = 1;
    goto LABEL_21;
  }
  if ( n21 == 21 )
    return sub_6500D334(a2, n512_2, n0x65011120, i, a6[4], a6[2], a6[3], 8 * *(_DWORD *)(a1 + 16), a7);
  if ( !n21 )
  {
    if ( !n512_2 )
    {
      sub_6500BB58(a2, (__int64)n0x65011120, i);
      n512_1 = i;
LABEL_21:
      *a7 = n512_1;
      return 0LL;
    }
    n0x65011120_1 = n0x65011120;
    goto LABEL_13;
  }
  return 4294901765LL;
}

//----- (000000006500D0B4) ----------------------------------------------------
__int64 __fastcall sub_6500D0B4(_BYTE *a1, int n48, __int64 a3)
{
  return sub_6500CC04(0x2000004u, 0, a1, n48, a3, 0LL, 0LL, 0, 0LL, 0, 0LL, 0);
}

//----- (000000006500D10C) ----------------------------------------------------
__int64 sub_6500D10C()
{
  return 0xFFFFFFFFLL;
}

//----- (000000006500D114) ----------------------------------------------------
__int64 __fastcall sub_6500D114(char a1, char a2, char a3, int a4)
{
  return a4 & 0x1FFFF | 0xB0000000 | ((a2 & 0x1F) << 18) | ((a1 & 0xF) << 23) | ((a3 & 1) << 17);
}

//----- (000000006500D13C) ----------------------------------------------------
__int64 __fastcall sub_6500D13C(char a1, char a2, char a3, int a4)
{
  return a4 & 0x1FFFF | 0xB8000000 | ((a2 & 0x1F) << 18) | ((a1 & 0xF) << 23) | ((a3 & 1) << 17);
}

//----- (000000006500D164) ----------------------------------------------------
__int64 __fastcall sub_6500D164(char a1, char a2, char a3, char a4)
{
  return ((a4 & 0x1F) << 6) | 0x58000000 | ((a3 & 0x1F) << 12) | ((a2 & 0x1F) << 18) | ((unsigned __int8)(a1 & 0xF) << 23);
}

//----- (000000006500D18C) ----------------------------------------------------
__int64 __fastcall sub_6500D18C(char a1, char a2)
{
  return ((a2 & 0x1F) << 18) | 0x8000000 | ((unsigned __int8)(a1 & 0xF) << 23);
}

//----- (000000006500D1A0) ----------------------------------------------------
__int64 __fastcall sub_6500D1A0(unsigned int n33554435, __int64 a2, unsigned __int64 n0x40, __int64 a4, __int64 i_1)
{
  __int64 v8; // x23
  __int64 result; // x0
  unsigned __int64 n20; // x25
  __int64 v12; // x1
  int v13; // w19
  _BYTE *v14; // x1
  unsigned __int64 i; // x1

  v8 = a4;
  if ( !a2 || !a4 || n33554435 - 33554434 > 2 )
    return 4294901767LL;
  if ( n33554435 == 33554434 )
  {
    n20 = 20LL;
  }
  else
  {
    n20 = 28LL;
    if ( n33554435 != 33554435 )
      n20 = 32LL;
  }
  sub_6500BB3C(0x65011120LL, 0, 64);
  sub_6500BB3C(0x650110D8LL, 0, 68);
  if ( n0x40 > 0x40 )
    return 4294901767LL;
  v12 = a2;
  v13 = 0;
  sub_6500BB58(0x650110D8LL, v12, n0x40);
  while ( i_1 )
  {
    v14 = (_BYTE *)(0x650110D8 + n0x40);
    *(_BYTE *)(0x650110D8 + n0x40) = HIBYTE(v13);
    v14[3] = v13;
    v14[1] = BYTE2(v13);
    v14[2] = BYTE1(v13);
    ++v13;
    result = sub_6500CC04(n33554435, 0, (_BYTE *)0x650110D8, (int)n0x40 + 4, 0x65011120LL, 0LL, 0LL, 0, 0LL, 0, 0LL, 0);
    if ( (_DWORD)result )
      return result;
    for ( i = 0LL; i_1 != i && i < n20; ++i )
      *(_BYTE *)(v8 + i) = *(_BYTE *)(i + 0x65011120);
    i_1 -= i;
    v8 += i;
  }
  return 0LL;
}

//----- (000000006500D334) ----------------------------------------------------
__int64 __fastcall sub_6500D334(
        __int64 a1,
        unsigned int i_2,
        unsigned __int8 *a3,
        int a4,
        unsigned int i_3,
        unsigned int n33554435,
        unsigned int n33554435_1,
        int a8,
        _DWORD *a9)
{
  unsigned int v13; // w20
  int n20; // w19
  unsigned int v15; // w7
  unsigned int i_4; // w23
  unsigned int i_1; // w28
  __int64 i; // x6
  __int64 v19; // x4
  unsigned int v20; // w23
  int v21; // w20
  char v23; // [xsp+80h] [xbp+60h]
  char v24; // [xsp+80h] [xbp+60h]

  if ( !a1 )
    return (unsigned int)-65529;
  if ( !a9 )
    return (unsigned int)-65529;
  *a9 = 1;
  if ( n33554435 - 33554434 > 2 )
    return (unsigned int)-65529;
  if ( n33554435 == 33554434 )
  {
    n20 = 20;
  }
  else if ( n33554435 == 33554435 )
  {
    n20 = 28;
  }
  else
  {
    n20 = 32;
  }
  v15 = a8 - 1;
  i_4 = v15 >> 3;
  if ( (v15 & 7) != 0 )
    ++i_4;
  if ( i_3 <= i_4 && i_4 >= i_3 + 2 + n20 )
  {
    v23 = v15;
    sub_6500BB3C(0x65011160LL, 0, 512);
    sub_6500BB3C(0x65011360LL, 0, 1024);
    sub_6500BB3C(0x65011760LL, 0, 64);
    if ( a3[a4 - 1] != 188 )
      return (unsigned int)-65528;
    i_1 = i_4 - 1 - n20;
    sub_6500BB58(0x65011160LL, (__int64)a3, i_1);
    sub_6500BB58(0x65011760LL, (__int64)&a3[i_1], n20);
    if ( (*a3 & ~(255 >> (8 * i_4 - v23))) != 0 )
      return (unsigned int)-65528;
    v24 = 255 >> (8 * i_4 - v23);
    v13 = sub_6500D1A0(n33554435_1, 0x65011760LL, n20 & 0x3C, 0x65011360LL, i_1);
    if ( v13 )
      return v13;
    for ( i = 0LL; i_1 > (unsigned int)i; ++i )
      *(_BYTE *)(i + 0x65011160) ^= *(_BYTE *)(i + 0x65011360);
    v19 = 0LL;
    MEMORY[0x65011160] &= v24;
    v20 = i_4 - i_3 - 2 - n20;
    while ( 1 )
    {
      v21 = v19;
      if ( (unsigned int)v19 >= v20 )
        break;
      ++v19;
      if ( *(_BYTE *)(0x65011160 + v19 - 1) )
        return (unsigned int)-65528;
    }
    if ( *(_BYTE *)(0x65011160LL + (unsigned int)v19) == 1 )
    {
      sub_6500BB3C(0x65011360LL, 0, 8);
      sub_6500BB58(0x65011368LL, a1, i_2);
      sub_6500BB58(0x65011360 + i_2 + 8LL, 0x65011160LL + (unsigned int)(v21 + 1), i_3);
      v13 = sub_6500CC04(n33554435, 0, (_BYTE *)0x65011360, i_2 + 8 + i_3, 0x65011360LL, 0LL, 0LL, 0, 0LL, 0, 0LL, 0);
      if ( !v13 && !(unsigned int)sub_6500BB78(0x65011360LL, 0x65011760LL, n20) )
        *a9 = 0;
    }
    else
    {
      return (unsigned int)-65528;
    }
  }
  else
  {
    return (unsigned int)-65529;
  }
  return v13;
}

//----- (000000006500D640) ----------------------------------------------------
__int64 __fastcall sub_6500D640(
        _BYTE *a1,
        unsigned int n0xA_1,
        unsigned int i,
        unsigned int n0xA,
        __int64 a5,
        unsigned int *a6,
        _DWORD *a7)
{
  __int64 result; // x0
  _BYTE *v9; // x9
  unsigned __int8 *v10; // x9
  unsigned int v11; // w9
  unsigned int n7; // w2
  unsigned int i_1; // w2

  if ( !a1 || !n0xA_1 || !a7 )
    return 4294901767LL;
  *a7 = 0;
  if ( n0xA_1 > n0xA || n0xA <= 0xA )
    return 4294901768LL;
  result = 4294901768LL;
  if ( !*a1 && (unsigned __int8)a1[1] == (unsigned __int8)i )
  {
    if ( i == 1 )
    {
      v10 = a1 + 2;
      for ( i = 2; i < n0xA; ++i )
      {
        if ( *v10 != 255 )
          break;
        ++v10;
      }
      result = 4294901768LL;
      if ( a1[i] )
        return result;
    }
    else
    {
      result = 4294901765LL;
      if ( i != 2 )
        return result;
      v9 = a1 + 2;
      do
      {
        if ( !*v9 )
          break;
        ++i;
        ++v9;
      }
      while ( i < n0xA );
    }
    v11 = i + 1;
    result = 4294901768LL;
    if ( i + 1 < n0xA )
    {
      n7 = i - 2;
      if ( n7 > 7 )
      {
        if ( a5 )
        {
          if ( a6 )
          {
            i_1 = n0xA_1 - 3 - n7;
            if ( *a6 >= i_1 )
            {
              *a6 = i_1;
              sub_6500BB58(a5, (__int64)&a1[v11], i_1);
              *a7 = 1;
              return 0LL;
            }
          }
        }
        return 4294901767LL;
      }
    }
  }
  return result;
}

//----- (000000006500D77C) ----------------------------------------------------
__int64 __fastcall sub_6500D77C(__int64 a1, __int64 a2)
{
  int v4; // w21
  int v5; // w22
  __int64 i_1; // x19
  __int64 n0x48; // x20
  unsigned int v8; // w20
  __int64 i_2; // x2
  __int64 i_3; // x19
  __int64 i; // [xsp+48h] [xbp+48h]
  _BYTE v13[72]; // [xsp+58h] [xbp+58h] BYREF

  if ( (unsigned int)sub_65002078((__int64)"TEE_CONFIG_HEADER", 3171155968LL) )
  {
    sub_650021A0("teecfg magic err!\n");
    while ( 1 )
      ;
  }
  v4 = 0;
  v5 = MEMORY[0xBD04002C];
  i_1 = (unsigned int)(MEMORY[0xBD040028] - 1123811328);
  while ( 1 )
  {
    if ( v4 == v5 )
      return (unsigned int)-1;
    n0x48 = (unsigned int)sub_6500A6F0((_BYTE *)i_1) + 1;
    if ( (unsigned int)n0x48 > 0x48 )
      return (unsigned int)-1;
    sub_6500201C((__int64)v13, i_1, (unsigned int)n0x48);
    i_2 = *(unsigned __int8 *)(i_1 + n0x48);
    i_3 = i_1 + n0x48 + 1;
    i = i_2;
    v8 = sub_65002078(a1, (__int64)v13);
    if ( !v8 )
      break;
    i_1 = i_3 + (unsigned __int8)i;
    ++v4;
  }
  sub_6500201C(a2, i_3, i);
  return v8;
}

//----- (000000006500D868) ----------------------------------------------------
__int64 sub_6500D868()
{
  unsigned __int64 v0; // x2
  unsigned int i; // w0
  _DWORD *v2; // x1
  unsigned int j; // w0
  _DWORD *v4; // x1
  __int64 n1686110360; // x0

  MEMORY[0x64803000] = MEMORY[0x64803000] & 0xFFFFFFCF | 0x10;
  MEMORY[0x64803008] = MEMORY[0x64803008] & 0xFFFFFFCF | 0x10;
  if ( MEMORY[0x82000020] == 1 )
  {
    v0 = MEMORY[0x82000028];
  }
  else
  {
    v0 = 0LL;
    if ( MEMORY[0x82000020] == 2 )
      v0 = MEMORY[0x82000030] + MEMORY[0x82000028];
  }
  MEMORY[0x64816780] = -1;
  for ( i = 0; i != 4; ++i )
  {
    v2 = (_DWORD *)(4LL * i + 1686202240);
    v2[2] = 0;
    v2[6] = 0;
    v2[10] = 0;
    v2[14] = 0;
  }
  MEMORY[0x64816784] = -1;
  MEMORY[0x64816780] = v0 >> 12;
  MEMORY[0x64821010] = 0x2000;
  MEMORY[0x64807024] = MEMORY[0x64807024] & 0xFFFFFFFC | 1;
  MEMORY[0x64807030] = MEMORY[0x64807030] & 0xFFFFFFFC | 1;
  MEMORY[0x6480A008] = 537919488;
  MEMORY[0x6480A048] = 0x40000000;
  MEMORY[0x6480A00C] = 537919492;
  MEMORY[0x6480A04C] = 0x100000;
  MEMORY[0x64816000] = -1;
  for ( j = 0; j != 4; ++j )
  {
    v4 = (_DWORD *)(4LL * j + 1686200320);
    v4[2] = -1;
    v4[6] = 0;
    v4[10] = -1;
    v4[14] = 0;
  }
  MEMORY[0x64816004] = 249951;
  MEMORY[0x64816000] = 249856;
  MEMORY[0x64804024] |= 0x80000u;
  MEMORY[0x64804048] = 1687159156;
  MEMORY[0x64804088] = 12288;
  MEMORY[0x64805078] = 1687225136;
  MEMORY[0x648050B8] = 0x2000000;
  MEMORY[0x64805080] = 1687227288;
  MEMORY[0x648050C0] = 2048;
  MEMORY[0x64805084] = 1687226392;
  MEMORY[0x648050C4] = 2;
  n1686110360 = 1686110360LL;
  MEMORY[0x64800098] = MEMORY[0x64800098] & 0xFFFFCFFF | 0x1000;
  return n1686110360;
}

//----- (000000006500DB1C) ----------------------------------------------------
__int64 __fastcall sub_6500DB1C(__int64 a1)
{
  unsigned int v1; // w1
  __int64 n1686200320; // x0

  v1 = *(_DWORD *)(a1 + 32) + 1023803391;
  n1686200320 = 1686200320LL;
  MEMORY[0x64816004] = v1 >> 12;
  return n1686200320;
}

//----- (000000006500DB40) ----------------------------------------------------
__int64 sub_6500DB40()
{
  unsigned int i; // w0
  _DWORD *v1; // x1
  unsigned int j; // w0
  _DWORD *v3; // x1
  __int64 n0x2000; // x0

  MEMORY[0x64808000] &= 0xFFFFFCFF;
  MEMORY[0x64808004] &= 0xFFFFFCFF;
  MEMORY[0x64808000] &= 0xFFFFFFF3;
  MEMORY[0x64808004] &= 0xFFFFFFF3;
  MEMORY[0x64803004] &= ~0x40u;
  MEMORY[0x6480300C] &= ~0x40u;
  MEMORY[0x6480D000] &= ~0x400u;
  MEMORY[0x6480D004] &= ~0x400u;
  MEMORY[0x64808000] &= 0xFFFFFFFC;
  MEMORY[0x64808004] &= 0xFFFFFFFC;
  MEMORY[0x64802000] = -1;
  for ( i = 0; i != 4; ++i )
  {
    v1 = (_DWORD *)(4LL * i + 1686118400);
    v1[2] = 0;
    v1[6] = 0;
    v1[10] = 0;
    v1[14] = 0;
  }
  MEMORY[0x64802008] = 3;
  MEMORY[0x64802028] = 3;
  MEMORY[0x64802010] = 0x2000;
  MEMORY[0x64802030] = 0x2000;
  MEMORY[0x64802004] = 127;
  MEMORY[0x64802000] = 0;
  MEMORY[0x64802080] = -1;
  for ( j = 0; j != 4; ++j )
  {
    v3 = (_DWORD *)(4LL * j + 1686118528);
    v3[2] = 0;
    v3[6] = 0;
    v3[10] = 0;
    v3[14] = 0;
  }
  MEMORY[0x64802088] = 3;
  MEMORY[0x648020A8] = 3;
  n0x2000 = 0x2000LL;
  MEMORY[0x64802090] = 0x2000;
  MEMORY[0x648020B0] = 0x2000;
  MEMORY[0x64802084] = 5631;
  MEMORY[0x64802080] = 5376;
  MEMORY[0x64822010] = 0x2000;
  return n0x2000;
}

// nfuncs=249 queued=235 decompiled=235 lumina nreq=0 worse=0 better=0
// ALL OK, 235 function(s) have been successfully decompiled
