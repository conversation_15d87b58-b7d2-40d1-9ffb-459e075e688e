#!/usr/bin/env python3
"""
最小化的uboot修复
只修改一个字节来利用逻辑错误
"""

import struct

def create_minimal_fix():
    """创建最小化修复"""
    print("=== 创建最小化uboot修复 ===")
    
    with open("uboot", 'rb') as f:
        data = bytearray(f.read())
    
    print(f"原始文件大小: {len(data)} 字节")
    
    # 根据之前的分析，签名数据结构在0x81c50
    sig_location = 0x81c50
    
    if sig_location < len(data):
        current_byte = data[sig_location]
        print(f"当前第一个字节: 0x{current_byte:02x} ({current_byte})")
        
        if current_byte == 1:
            print("当前使用分支2 (有逻辑错误)")
            print("修改为0，使用分支1 (正确逻辑)")
            
            # 只修改这一个字节
            data[sig_location] = 0
            
            with open("uboot_fixed", 'wb') as f:
                f.write(data)
            
            print("已创建 uboot_fixed")
            print("只修改了1个字节，应该不会影响启动")
            
        elif current_byte == 0:
            print("当前使用分支1 (正确逻辑)")
            print("修改为1，使用分支2 (有逻辑错误)")
            
            # 只修改这一个字节
            data[sig_location] = 1
            
            with open("uboot_test", 'wb') as f:
                f.write(data)
            
            print("已创建 uboot_test")
            print("只修改了1个字节，应该不会影响启动")
        
        else:
            print(f"未知的第一个字节值: {current_byte}")
    
    else:
        print("签名数据位置超出文件范围")

if __name__ == "__main__":
    create_minimal_fix()
