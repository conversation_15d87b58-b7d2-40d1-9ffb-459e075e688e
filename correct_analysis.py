#!/usr/bin/env python3
"""
正确的uboot文件结构分析
重新理解SPL代码中的数据结构
"""

import struct
import sys
import os

def hex_dump(data, offset=0, length=16):
    """十六进制转储函数"""
    result = []
    for i in range(0, len(data), length):
        hex_str = ' '.join(f'{b:02x}' for b in data[i:i+length])
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+length])
        result.append(f'{offset+i:08x}: {hex_str:<48} {ascii_str}')
    return '\n'.join(result)

def analyze_uboot_structure(filename):
    """正确分析uboot文件结构"""
    print(f"=== 分析uboot文件结构: {filename} ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    print(f"文件大小: {len(data)} 字节 (0x{len(data):x})")
    print()
    
    # 显示文件头
    print("文件头部:")
    print(hex_dump(data[:64]))
    print()
    
    if len(data) < 52:
        print("文件太小")
        return
    
    # 读取偏移48处的值
    offset_48 = struct.unpack('<I', data[48:52])[0]
    print(f"偏移48的值: 0x{offset_48:x} ({offset_48})")
    
    # 根据SPL代码，这些是关键位置：
    # v3 = a2 + *(unsigned int *)(a2 + 48) + 512
    v3_location = offset_48 + 512
    print(f"v3位置 (offset_48 + 512): 0x{v3_location:x}")
    
    if v3_location + 20 < len(data):
        print(f"v3位置的数据:")
        print(hex_dump(data[v3_location:v3_location+64], v3_location))
        
        # 哈希长度字段在 v3 + 16
        hash_length = struct.unpack('<I', data[v3_location+16:v3_location+20])[0]
        print(f"哈希计算长度 (v3+16): {hash_length}")
        print()
    
    # 签名数据结构位置计算
    # v6 = a1 + *(_QWORD *)(a1 + *(unsigned int *)(a1 + 48) + 552)
    offset_552_location = offset_48 + 552
    if offset_552_location + 8 < len(data):
        offset_552_value = struct.unpack('<Q', data[offset_552_location:offset_552_location+8])[0]
        print(f"偏移552位置: 0x{offset_552_location:x}")
        print(f"偏移552的值: 0x{offset_552_value:x}")
        
        # 签名数据结构的实际位置
        sig_data_location = offset_552_value
        print(f"签名数据结构位置: 0x{sig_data_location:x}")
        
        if sig_data_location < len(data):
            print(f"签名数据结构:")
            print(hex_dump(data[sig_data_location:sig_data_location+64], sig_data_location))
            
            # 读取第一个字节 - 控制验证分支
            first_byte = data[sig_data_location]
            print(f"第一个字节 (控制分支): 0x{first_byte:02x} ({first_byte})")
            
            return sig_data_location, first_byte
        else:
            print("签名数据结构位置超出文件范围")
    
    return None, None

def find_real_bug(filename):
    """寻找真正的bug"""
    print(f"\n=== 寻找真正的bug ===")
    
    with open(filename, 'rb') as f:
        data = f.read()
    
    # 让我们查看verify_image_signature函数的参数
    # a1: 计算的哈希 (来自sub_6500C108)
    # a2: 实际哈希 (来自sub_6500BBA8计算)  
    # a3: 签名数据结构 (来自sub_6500C094)
    
    sig_location, first_byte = analyze_uboot_structure(filename)
    
    if sig_location is not None:
        print(f"\n当前签名数据第一个字节: {first_byte}")
        
        if first_byte == 0:
            print("使用分支1: if ( !(unsigned int)memory_compare(a2, v9, 32) )")
            print("逻辑: 哈希相等时继续")
        elif first_byte == 1:
            print("使用分支2: if ( (unsigned int)memory_compare(a2, v9, 32) ) return 0;")
            print("逻辑: 哈希不相等时返回失败")
        
        print("\n=== 可能的bug ===")
        print("1. 两个分支的逻辑相反，可能存在某种情况下的绕过")
        print("2. 哈希计算长度可能被控制为0")
        print("3. 签名数据结构的位置可能被操控")
        
        # 检查是否可以简单修改第一个字节
        print(f"\n尝试修改第一个字节从 {first_byte} 到其他值...")
        
        return True
    
    return False

def create_minimal_exploit(filename):
    """创建最小化的利用"""
    print(f"\n=== 创建最小化利用 ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    sig_location, current_byte = analyze_uboot_structure(data)
    
    if sig_location is not None:
        print(f"当前第一个字节: {current_byte}")
        
        # 只修改第一个字节，尝试不同的值
        for test_value in [0, 1]:
            if test_value != current_byte:
                test_data = data.copy()
                test_data[sig_location] = test_value
                
                output_filename = f"uboot_minimal_{test_value}"
                with open(output_filename, 'wb') as f:
                    f.write(test_data)
                
                print(f"创建: {output_filename} (第一个字节: {test_value})")
        
        print("\n这些文件只修改了一个字节，应该不会破坏启动")

def main():
    filename = "uboot"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print("=== 正确的uboot签名绕过分析 ===")
    
    if find_real_bug(filename):
        create_minimal_exploit(filename)
    
    print("\n=== 总结 ===")
    print("创建了最小化的修改文件，只改变一个字节")
    print("这应该不会破坏uboot的启动功能")
    print("如果仍然不能启动，说明bug可能在其他地方")

if __name__ == "__main__":
    main()
