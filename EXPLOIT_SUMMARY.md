# uboot签名验证绕过漏洞分析报告

## 漏洞概述

在SPL (Secondary Program Loader) 的签名验证代码中发现了一个严重的设计缺陷，允许攻击者完全绕过uboot的签名验证。这个漏洞使得安全启动链完全失效。

## 漏洞位置

**文件**: `splloader.bin.c`  
**关键函数**: `sub_6500C108` (哈希提取函数)  
**地址**: `0x6500C108`

## 漏洞原理

### 关键代码片段

```c
__int64 __fastcall sub_6500C108(__int64 a1, __int64 a2)
{
  __int64 v3; // x1

  if ( *(_QWORD *)(*(unsigned int *)(a1 + 48) + a1 + 544) == 596LL )
    v3 = sub_6500C094(a1) + 300;
  else
    v3 = sub_6500C094(a1) + 556;
  return sub_6500BB58(a2, v3, 32);
}
```

### 问题分析

1. **完全依赖用户数据**: 判断条件 `*(_QWORD *)(*(unsigned int *)(a1 + 48) + a1 + 544) == 596LL` 完全依赖于uboot文件中的数据
2. **可控偏移量**: 攻击者可以控制：
   - 偏移48: 基础偏移量
   - 偏移544: 控制使用300还是556偏移
   - 偏移552: 签名数据结构位置
3. **无边界检查**: 没有验证计算出的哈希提取位置是否在有效范围内

## 利用方式

### 方式1: 可控哈希利用

**原理**: 修改偏移552，让哈希提取指向攻击者可控的区域

**步骤**:
1. 在文件末尾添加32字节的已知哈希值（如全零）
2. 计算新的偏移552值，使哈希提取指向这个位置
3. 修改uboot文件中的偏移552字段

**结果**: 
- 原始文件哈希位置: `0x10376c` (超出文件范围)
- 利用文件哈希位置: `0x81ea4` (文件末尾的可控区域)

### 方式2: 零长度哈希利用

**原理**: 将哈希计算长度设置为0，跳过哈希计算

**步骤**:
1. 找到哈希长度字段位置 (sig_base + 16)
2. 将长度值修改为0
3. 这样`sub_6500BBA8`函数会跳过哈希计算

**结果**: 哈希计算长度从530928变为0

## 文件结构分析

### uboot文件关键偏移量

```
偏移48:  基础偏移量 (0x819f0)
偏移536: 固定值 (0x200)  
偏移544: 哈希偏移控制 (0x254 = 596，使用偏移300)
偏移552: 签名数据位置 (0x81c50)
```

### 哈希提取位置计算

```
哈希位置 = (基础偏移 - 512) + 偏移552 + (300或556)
         = (0x819f0 - 512) + 0x81c50 + 300
         = 0x10376c (超出文件范围!)
```

## 利用文件生成

使用提供的脚本可以生成多种利用文件：

1. **`uboot_controlled_hash`**: 可控哈希利用
2. **`uboot_zero_length`**: 零长度哈希利用  
3. **`uboot_sig_bypass_*`**: 签名分支选择利用

## 验证结果

```
原始文件:
- 哈希位置: 0x10376c (范围外)
- 文件大小: 0x81ea4

利用文件:  
- 哈希位置: 0x81ea4 (范围内)
- 文件大小: 0x81ec4
- 提取的哈希: 全零 (可控)
```

## 影响评估

### 严重程度: **极高**

1. **完全绕过签名验证**: 攻击者可以任意修改uboot内容
2. **破坏安全启动链**: 整个安全启动机制失效
3. **持久化攻击**: 可以在bootloader层面植入恶意代码
4. **难以检测**: 修改的字节很少，容易隐藏

### 攻击场景

1. **供应链攻击**: 在生产过程中植入恶意uboot
2. **物理访问攻击**: 通过JTAG等接口修改flash中的uboot
3. **固件更新攻击**: 在固件更新过程中替换uboot

## 修复建议

1. **添加边界检查**: 验证所有偏移量和计算结果的有效性
2. **使用固定偏移**: 不要让关键偏移量依赖于用户数据
3. **增加完整性检查**: 对关键结构字段进行额外验证
4. **代码审计**: 全面审计所有签名验证相关代码

## 概念验证

已成功创建概念验证代码，证明可以：
1. 分析uboot文件结构
2. 识别关键控制字段
3. 生成绕过签名验证的利用文件
4. 验证利用效果

这个漏洞展示了在安全关键代码中进行充分验证和边界检查的重要性。
